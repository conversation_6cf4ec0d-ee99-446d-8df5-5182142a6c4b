# 🔌 Cyzer - Granular Code Analysis Tool

A sophisticated code analysis tool with a **granular plugin architecture** designed to extract relevant context from codebases for LLM assistance. Each plugin is self-contained and designed as a future npm package.

## 🎯 **Key Features**

- **🔌 Granular Plugin Architecture** - Self-contained plugins for future npm distribution
- **🎯 Auto-Detection** - Automatically detects project type and loads appropriate plugins
- **🔧 Plugin Composition** - Clean dependency chain (JavaScript → TypeScript → NestJS)
- **🚀 Framework-Aware** - Special handling for NestJS, React, and other frameworks
- **📊 Smart Context Extraction** - Token-efficient output optimized for LLM context limits
- **🧪 Comprehensive Testing** - Unit tests, integration tests, real repository testing

## 🏗️ **Current Architecture**

### **📁 Self-Contained Plugins**
```
src/plugins/
├── javascript-plugin/          # Future: @cyzer/plugin-javascript
├── typescript-plugin/          # Future: @cyzer/plugin-typescript  
├── nestjs-plugin/             # Future: @cyzer/plugin-nestjs
├── react-plugin/              # Future: @cyzer/plugin-react
└── PluginRegistry.ts          # Plugin management & auto-detection
```

### **🔧 Plugin Capabilities**
- ✅ **JavaScript Plugin** - Functions, classes, variables, imports/exports
- ✅ **TypeScript Plugin** - Interfaces, types, decorators (includes decorator analysis)
- ✅ **NestJS Plugin** - Controllers, services, modules, DI patterns, endpoints
- ✅ **React Plugin** - Components, hooks, contexts, JSX patterns

## 🚀 **Quick Start**

### **Installation & Setup**
```bash
# Install dependencies
npm install

# Run all tests
npm run test:all

# Clone test repositories for validation
npm run clone-test-repos
```

### **Testing the Plugin System**
```bash
# Test individual plugins
npm run test:plugins

# Test plugin integration
npm run test:integration

# Test on real repositories
npm run test:real-repos
```

### **Current Usage (Development)**
```typescript
// Auto-detect project type and analyze
import { createPluginRegistry } from './src/plugins/PluginRegistry.js';

const registry = await createPluginRegistry('./my-nestjs-project');
// Auto-detects: "nestjs-backend" configuration
// Loads: typescript-plugin + nestjs-plugin

const plugins = registry.getPluginsForFile('users.controller.ts', 'typescript', 'nestjs');
const result = await plugins[0].analyze({
  filePath: 'users.controller.ts',
  fileContent: '...',
  language: 'typescript',
  framework: 'nestjs'
});

console.log(result.metadata.architecture);
// Output: { controllers: 1, services: 0, modules: 0, endpoints: 5 }
```

## 📊 **Implementation Status**

### **✅ Completed (Phase 1)**
- **Plugin Architecture** - Self-contained, composable plugins
- **Core Plugins** - JavaScript, TypeScript, NestJS, React
- **Auto-Detection** - Project type detection and plugin loading
- **Testing Infrastructure** - Comprehensive test suite
- **Basic Analysis** - Pattern matching for code elements

### **🔄 In Progress (Phase 2)**
- **Tree-sitter Integration** - Plugin-specific queries
- **TypeScript Compiler API** - Full type information
- **Performance Optimization** - Large codebase handling

### **⏳ Next (Phase 3)**
- **CLI Interface** - `cyzer analyze`, `cyzer context`
- **Context Extraction** - Smart filtering and relevance scoring
- **Production Ready** - Performance, caching, incremental analysis

## 🧪 **Testing & Validation**

### **Test Repositories**
The system is tested on real-world repositories:
- **Small projects** (< 10k lines): `nestjs-realworld-example-app`
- **Medium projects** (10k-100k lines): `express`, `create-react-app`
- **Large projects** (100k+ lines): `nest`, `TypeScript`, `react`

### **Performance Targets**
- **Small projects**: < 5 seconds, < 100MB, > 95% accuracy
- **Medium projects**: < 30 seconds, < 500MB, > 90% accuracy
- **Large projects**: < 2 minutes, < 2GB, > 85% accuracy

## 🔧 **Development**

### **Adding a New Plugin**
1. **Create plugin folder**: `src/plugins/my-plugin/`
2. **Implement plugin**: `MyPlugin.ts` with `AnalysisPlugin` interface
3. **Add queries**: `queries/my-patterns.scm` (tree-sitter queries)
4. **Create tests**: `tests/plugins/MyPlugin.test.ts`
5. **Register plugin**: Add to `PluginRegistry.ts`

### **Plugin Interface**
```typescript
interface AnalysisPlugin {
  metadata: PluginMetadata;
  initialize(projectRoot: string, options?: any): Promise<void>;
  cleanup(): Promise<void>;
  canHandle(filePath: string, language: string, framework?: string): boolean;
  analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult>;
}
```

## 📚 **Documentation**

- **[Development Plan](docs/development-plan.md)** - Roadmap and current status
- **[Plugin Architecture](docs/README.md)** - Detailed architecture documentation
- **[Tool Comparison](docs/tool-comparison.md)** - Analysis approach comparison
- **[Implementation Summary](docs/implementation-summary.md)** - What's done and what's next

## 🎯 **Future Vision**

### **NPM Package Distribution**
```bash
# Core package with plugin system
npm install @cyzer/core

# Individual plugins
npm install @cyzer/plugin-javascript
npm install @cyzer/plugin-nestjs

# CLI with all plugins
npm install @cyzer/cli
```

### **Production Usage**
```bash
# Analyze entire project
cyzer analyze . --config nestjs-backend

# Extract context for specific file
cyzer context src/users/users.controller.ts --max-tokens 1000

# Output structured JSON
cyzer analyze . --output json --file analysis.json
```

**The granular plugin architecture is designed for scalability, maintainability, and future npm package distribution!** 🚀
