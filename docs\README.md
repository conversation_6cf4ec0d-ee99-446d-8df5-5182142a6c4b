# 🔌 Cyzer Plugin Architecture Documentation

## 📋 **Overview**

<PERSON><PERSON> uses a **granular, self-contained plugin architecture** where each plugin is organized as a future npm package. This design enables modular analysis of different languages and frameworks while maintaining clean separation of concerns.

## 🏗️ **Current Architecture**

### **📁 Plugin Organization**

```
src/plugins/
├── javascript-plugin/          # Self-contained JavaScript analysis
│   ├── index.ts               # Main export
│   ├── JavaScriptPlugin.ts    # Plugin implementation
│   ├── queries/               # Tree-sitter queries
│   │   └── javascript-base.scm
│   └── package.json           # Future: @cyzer/plugin-javascript
│
├── typescript-plugin/          # TypeScript + Decorators
│   ├── index.ts               # Main export
│   ├── TypeScriptPlugin.ts    # Plugin with decorator analysis
│   ├── queries/               # TypeScript-specific queries
│   │   ├── typescript-base.scm
│   │   └── decorators.scm
│   └── package.json           # Future: @cyzer/plugin-typescript
│
├── nestjs-plugin/             # NestJS framework analysis
│   ├── index.ts               # Main export
│   ├── NestJSPlugin.ts        # Framework-specific patterns
│   ├── queries/               # NestJS-specific queries
│   │   └── nestjs-patterns.scm
│   └── package.json           # Future: @cyzer/plugin-nestjs
│
├── react-plugin/              # React framework analysis
│   ├── index.ts               # Main export
│   ├── ReactPlugin.ts         # React-specific patterns
│   ├── queries/               # React-specific queries
│   │   └── react-patterns.scm
│   └── package.json           # Future: @cyzer/plugin-react
│
└── PluginRegistry.ts          # Plugin management & auto-detection
```

### **🔧 Plugin Composition Pattern**

```typescript
// Clean dependency chain (no circular dependencies)
JavaScript Plugin (base)
    ↓
TypeScript Plugin (includes decorators)
    ↓
NestJS Plugin (uses TypeScript + decorators)

// React Plugin (parallel branch)
JavaScript Plugin (base)
    ↓
React Plugin (can also use TypeScript)
```

## ✅ **Implementation Status**

### **🟢 Completed Features**

#### **Core Plugin System**
- ✅ **Self-contained plugin architecture** - Each plugin has all its assets
- ✅ **Plugin composition pattern** - Plugins use other plugins as dependencies
- ✅ **Plugin registry with auto-detection** - Detects project type automatically
- ✅ **Configuration management** - Predefined configs for different project types

#### **JavaScript Plugin** (`javascript-plugin/`)
- ✅ **Function detection** (regular, arrow, async)
- ✅ **Class detection** with methods
- ✅ **Variable detection** (const, let, var)
- ✅ **Import/Export detection** (CommonJS, ES modules)
- ✅ **Basic pattern matching** implementation

#### **TypeScript Plugin** (`typescript-plugin/`)
- ✅ **Builds on JavaScript plugin** (composition pattern)
- ✅ **Interface detection** with properties
- ✅ **Type alias detection** (union types, generics)
- ✅ **Decorator detection** (class, method, parameter decorators)
- ✅ **Generic type support** (basic pattern matching)

#### **NestJS Plugin** (`nestjs-plugin/`)
- ✅ **Controller detection** (@Controller decorator)
- ✅ **Service detection** (@Injectable decorator)
- ✅ **Module detection** (@Module decorator)
- ✅ **Endpoint detection** (@Get, @Post, @Put, @Delete)
- ✅ **Guard/Interceptor detection** (@UseGuards, @UseInterceptors)
- ✅ **DI pattern detection** (@Inject, @InjectRepository)
- ✅ **Architecture extraction** (controllers, services, modules count)

#### **React Plugin** (`react-plugin/`)
- ✅ **Component detection** (function and class components)
- ✅ **Hook detection** (built-in and custom hooks)
- ✅ **Context detection** (createContext, useContext)
- ✅ **JSX detection** and analysis

#### **Testing Infrastructure**
- ✅ **Comprehensive unit tests** for all plugins
- ✅ **Integration tests** for plugin system
- ✅ **Test files** with real-world patterns
- ✅ **Real repository testing** framework
- ✅ **Performance metrics** collection

### **🔄 In Progress**

#### **Tree-sitter Integration**
- 🔄 **Plugin-specific queries** - Move from centralized to plugin folders ✅
- 🔄 **Tree-sitter parser integration** - Connect queries to plugins
- 🔄 **Performance optimization** - Efficient parsing

#### **TypeScript Compiler API**
- 🔄 **Full type information** - Integrate TypeScript Compiler API
- 🔄 **Cross-file analysis** - Import/export resolution
- 🔄 **Semantic analysis** - Type relationships

### **📋 TODO (Next Phase)**

#### **CLI Integration**
- ⏳ **Command-line interface** - `cyzer analyze`, `cyzer context`
- ⏳ **JSON output format** - Structured analysis results
- ⏳ **Configuration files** - Project-specific settings

#### **Context Extraction & Filtering**
- ⏳ **Relevance scoring** - Smart context filtering
- ⏳ **Token-aware truncation** - Fit LLM context limits
- ⏳ **Dependency graph** - Cross-file relationships
- ⏳ **Call graph generation** - Function call relationships

#### **Performance & Scalability**
- ⏳ **Incremental analysis** - Only re-analyze changed files
- ⏳ **Caching system** - Store processed results
- ⏳ **Memory optimization** - Handle large codebases
- ⏳ **Parallel processing** - Multi-threaded analysis

#### **Additional Plugins**
- ⏳ **Express.js plugin** - Express-specific patterns
- ⏳ **Vue.js plugin** - Vue framework analysis
- ⏳ **Angular plugin** - Angular framework analysis
- ⏳ **Testing plugins** - Jest, Mocha, Vitest patterns

## 🎯 **Plugin Configurations**

### **Auto-Detection**
```typescript
// Automatically detects project type and loads appropriate plugins
const registry = await createPluginRegistry('./my-project');

// Detects:
// - NestJS projects (has @nestjs/* dependencies)
// - React projects (has react dependencies)  
// - TypeScript projects (has tsconfig.json)
// - JavaScript projects (fallback)
```

### **Manual Configuration**
```typescript
// Load specific configuration
await registry.loadConfiguration('nestjs-backend', './my-project');

// Available configurations:
// - 'nestjs-backend': TypeScript + NestJS plugins
// - 'react-frontend': JavaScript + TypeScript + React plugins
// - 'typescript-project': TypeScript plugin only
// - 'javascript-project': JavaScript plugin only
```

## 🧪 **Testing & Validation**

### **Test Commands**
```bash
# Unit tests for individual plugins
npm run test:plugins

# Integration tests for plugin system
npm run test:integration

# All plugin tests
npm run test:all

# Test on real repositories
npm run test:real-repos

# Clone test repositories
npm run clone-test-repos
```

### **Test Repositories**
- **Small projects** (< 10k lines): `nestjs-realworld-example-app`
- **Medium projects** (10k-100k lines): `express`, `create-react-app`
- **Large projects** (100k+ lines): `nest`, `TypeScript`, `react`

### **Performance Targets**
- **Small projects**: < 5 seconds, < 100MB, > 95% accuracy
- **Medium projects**: < 30 seconds, < 500MB, > 90% accuracy
- **Large projects**: < 2 minutes, < 2GB, > 85% accuracy

## 🚀 **Getting Started**

### **Development Setup**
```bash
# Install dependencies
npm install

# Run tests
npm run test:all

# Clone test repositories
npm run clone-test-repos

# Test on real repositories
npm run test:real-repos
```

### **Adding a New Plugin**
1. **Create plugin folder**: `src/plugins/my-plugin/`
2. **Implement plugin**: `MyPlugin.ts` with `AnalysisPlugin` interface
3. **Add queries**: `queries/my-patterns.scm` (tree-sitter queries)
4. **Create index**: `index.ts` with exports and metadata
5. **Add tests**: `tests/plugins/MyPlugin.test.ts`
6. **Register plugin**: Add to `PluginRegistry.ts`

### **Plugin Interface**
```typescript
interface AnalysisPlugin {
  metadata: PluginMetadata;
  initialize(projectRoot: string, options?: any): Promise<void>;
  cleanup(): Promise<void>;
  canHandle(filePath: string, language: string, framework?: string): boolean;
  analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult>;
}
```

## 📚 **Additional Documentation**

- **[Development Plan](development-plan.md)** - Detailed roadmap and milestones
- **[Implementation Summary](implementation-summary.md)** - Current status and next steps
- **[Tool Comparison](tool-comparison.md)** - Analysis of different parsing approaches

## 🎯 **Key Design Decisions**

### **✅ Self-Contained Plugins**
- Each plugin folder contains all its assets (queries, types, analyzers)
- Future npm packages: `@cyzer/plugin-javascript`, `@cyzer/plugin-nestjs`
- Independent development and testing

### **✅ Decorators in TypeScript Plugin**
- Decorators are TypeScript-first feature
- Better integration with TypeScript Compiler API
- Framework plugins interpret generic decorators as specific patterns

### **✅ Composition Over Inheritance**
- NestJS plugin uses TypeScript plugin as dependency
- Clear dependency chain without circular references
- Shared data through plugin results

**The architecture is designed for scalability, maintainability, and future npm package distribution!** 🚀
