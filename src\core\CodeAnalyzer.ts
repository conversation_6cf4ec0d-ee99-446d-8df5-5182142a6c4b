import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';
import { PluginRegistry } from '../plugins/PluginRegistry.js';
import {
    FileAnalysisResult, ProjectAnalysisResult, AnalysisOptions,
    CodeElement, CodeElementType, ImportElement, ExportElement,
    EmbeddedCodeChunk, SearchResult, MetadataFilters // New types
} from '../types/CodeElement.js';
import { EmbeddingService } from '../services/embedding/EmbeddingService.js'; // New service
import { VectorSearchService } from '../services/embedding/VectorSearchService.js'; // New service
import { DirectedGraph } from 'graphology';
import writeGraphML from 'graphology-graphml';
import ignore, { Ignore } from 'ignore';

export class CodeAnalyzer {
    private pluginRegistry: PluginRegistry;
    private projectRoot: string;
    private fileExtensionMap: Map<string, string[]> = new Map(); // Cache resolved file paths by base path
    private embeddingService: EmbeddingService;
    private vectorSearchService: VectorSearchService;

    constructor(projectRoot: string, configurationName?: string) {
        this.projectRoot = path.resolve(projectRoot);
        this.pluginRegistry = new PluginRegistry();
        this.embeddingService = new EmbeddingService();
        this.vectorSearchService = new VectorSearchService();
        console.log(`CodeAnalyzer initialized for root: ${this.projectRoot}`);
    }

    async initialize(configurationName?: string) {
        try {
            // Auto-detect and load configuration if not provided
            if (!configurationName) {
                configurationName = await this.pluginRegistry.autoDetectAndLoad(this.projectRoot);
            } else {
                // Load the specific configuration
                await this.pluginRegistry.loadConfiguration(configurationName, this.projectRoot);
            }

            await this.buildFileExtensionMap(); // Pre-build map for faster resolution
        } catch (error) {
            console.error(`Initialization failed: ${error}`);
            throw error; // Re-throw to indicate failure
        }
    }

    // Build a map for resolving module paths without extensions
    private async buildFileExtensionMap() {
        this.fileExtensionMap.clear();
        const allFiles = await this.findProjectFiles(true); // Find all files initially
        allFiles.forEach(filePath => {
            const parsedPath = path.parse(filePath);
            const basePath = path.join(parsedPath.dir, parsedPath.name); // Path without extension
            if (!this.fileExtensionMap.has(basePath)) {
                this.fileExtensionMap.set(basePath, []);
            }
            this.fileExtensionMap.get(basePath)?.push(filePath); // Store full path
        });
    }

    async analyzeProject(options: AnalysisOptions = {}): Promise<ProjectAnalysisResult> {
        const results: ProjectAnalysisResult = { files: [], errors: [], projectRoot: this.projectRoot };
        const filePaths = await this.findProjectFiles(); // Get analyzable files respecting .gitignore

        console.log(`Analyzing ${filePaths.length} files...`);

        const analysisPromises = filePaths.map(async (relativePath) => {
            const absolutePath = path.join(this.projectRoot, relativePath);
            try {
                const content = await fs.readFile(absolutePath, 'utf-8');
                const fileResult = await this.pluginRegistry.analyzeFile(content, relativePath);

                // Optional: Filter elements based on options.targetElementTypes here
                if (options.targetElementTypes && options.targetElementTypes.length > 0) {
                   fileResult.elements = fileResult.elements.filter(el => options.targetElementTypes?.includes(el.type));
                }
                // Optional: Remove fullText if not requested
                if (!options.includeElementContent) {
                    fileResult.elements.forEach(el => delete el.fullText);
                }

                if (fileResult.errors && fileResult.errors.length > 0) {
                    console.warn(`Errors analyzing ${relativePath}: ${fileResult.errors.join(', ')}`);
                }
                return fileResult;
            } catch (error: any) {
                console.error(`Failed to read or parse file ${relativePath}:`, error);
                return { filePath: relativePath, elements: [], errors: [`Read/Parse failed: ${error.message}`] };
            }
        });

        results.files = await Promise.all(analysisPromises);

        try {
            results.relations = this.buildRelations(results.files);
            // Convert graphology graph to a serializable format if needed for JSON output
            // results.relations = (results.relations as Graph).export();
        } catch (relationError: any) {
             results.errors?.push(`Failed to build relations: ${relationError.message}`);
             console.error("Error building relations:", relationError);
             results.relations = null; // Indicate failure
        }

        results.summary = this.summarizeResults(results.files);

        if (options.indexForSearch) {
            console.log('Generating embeddings and indexing content...');
            // Ensure element content was included if indexing is requested
            if (!options.includeElementContent) {
                console.warn('Warning: indexForSearch is true, but includeElementContent was false. Forcing includeElementContent for indexing.');
                // Re-run a minimal parse to get fullText if necessary, or ensure it's always on when indexForSearch is true.
                // For simplicity now, we assume analyzeProject was called with includeElementContent=true if indexForSearch=true.
                // A more robust solution would re-fetch or ensure content is present.
            }

            await this.vectorSearchService.clearIndex(); // Clear previous index for a full re-analysis
            let allElementsToIndex: CodeElement[] = [];
            results.files.forEach(fileResult => {
                allElementsToIndex.push(...fileResult.elements);
            });

            if (allElementsToIndex.length > 0) {
                const embeddedChunks = await this.embeddingService.generateEmbeddings(allElementsToIndex);
                await this.vectorSearchService.indexChunks(embeddedChunks);
                console.log(`Indexed ${this.vectorSearchService.getIndexSize()} code chunks for search.`);
            } else {
                console.log('No elements found to index for search.');
            }
        }

        console.log(`Analysis complete.`);
        return results;
    }

    // Corrected to properly handle .gitignore and file discovery
    private async findProjectFiles(findAllForMap: boolean = false): Promise<string[]> {
        const pattern = '**/*.{js,jsx,ts,tsx,mjs,cjs}';
        const defaultIgnores = ['node_modules/**', 'dist/**', '.*/**', '*.d.ts'];

        try {
            // Glob all potentially relevant files first.
            const files = await glob(pattern, {
                cwd: this.projectRoot,
                nodir: true,
                dot: false, // Ignores dot-folders like .git
            });

            const ig = ignore().add(defaultIgnores);

            if (!findAllForMap) {
                const gitignorePath = path.join(this.projectRoot, '.gitignore');
                try {
                    const gitignoreContent = await fs.readFile(gitignorePath, 'utf-8');
                    ig.add(gitignoreContent);
                    console.log(`Loaded .gitignore rules from ${gitignorePath}`);
                } catch {
                    // No .gitignore found, proceed with default ignores
                }
            }

            const filteredFiles = files.filter(f => !ig.ignores(f));
            
            // Normalize paths to use forward slashes for consistency
            return filteredFiles.map(f => f.replace(/\\/g, '/'));
        } catch (error) {
            console.error('Error finding project files:', error);
            return [];
        }
    }

    async semanticSearch(queryText: string, topK: number = 5, filters?: MetadataFilters): Promise<SearchResult[]> {
        console.log(`Performing semantic search for: "${queryText}"...`);
        const queryVector = await this.embeddingService.embedQuery(queryText);
        if (!queryVector) {
            console.error('Failed to generate query vector. Cannot perform search.');
            return [];
        }
        return this.vectorSearchService.search(queryVector, topK, filters);
    }

    // Resolve relative imports, including index files and extension guessing
    private resolveImportPath(importerPath: string, importSource: string): string | null {
        if (!importSource || !importSource.startsWith('.')) {
            // TODO: Handle node_modules or aliased paths later
            return null; // For now, only resolve relative paths
        }

        const importerDir = path.dirname(importerPath);
        let targetPath = path.normalize(path.join(importerDir, importSource)); // Normalize ./ and ../

        // Check direct match first (including potential extension in importSource)
        if (this.fileExtensionMap.has(targetPath) && this.fileExtensionMap.get(targetPath)?.length === 1) {
            // If targetPath itself exists in the map (e.g., import './file.js')
             return this.fileExtensionMap.get(targetPath)![0];
        }

        // Try resolving without extension (most common case)
        const possibleFiles = this.fileExtensionMap.get(targetPath);
        if (possibleFiles && possibleFiles.length > 0) {
            // Prioritize .ts/.tsx over .js/.jsx if both exist? Or just take the first found?
             // Simple approach: take the first one found by glob (which might not be deterministic)
             // More robust: Prefer specific extensions if multiple match the base path.
             const preferredExt = ['.ts', '.tsx', '.js', '.jsx', '.mjs', '.cjs'];
             for (const ext of preferredExt) {
                 const match = possibleFiles.find(f => f === targetPath + ext);
                 if(match) return match;
             }
             return possibleFiles[0]; // Fallback to first found
        }

        // Try resolving as index file (e.g., import './directory')
        const indexTargetPath = path.join(targetPath, 'index');
        const possibleIndexFiles = this.fileExtensionMap.get(indexTargetPath);
        if (possibleIndexFiles && possibleIndexFiles.length > 0) {
             const preferredExt = ['.ts', '.tsx', '.js', '.jsx', '.mjs', '.cjs'];
              for (const ext of preferredExt) {
                 const match = possibleIndexFiles.find(f => f === indexTargetPath + ext);
                 if(match) return match;
             }
            return possibleIndexFiles[0]; // Fallback
        }

        // console.warn(`Could not resolve relative import "${importSource}" from "${importerPath}"`);
        return null; // Resolution failed
    }


    private buildRelations(fileResults: FileAnalysisResult[]): any {
        console.log("Building relations graph...");
        const graph = new DirectedGraph({ multi: false, allowSelfLoops: false });

        const fileNodeMap: Map<string, string> = new Map(); // filePath -> nodeId

        // 1. Add nodes for each analyzed file
        fileResults.forEach(fileResult => {
            const nodeId = `file:${fileResult.filePath}`;
            if (!graph.hasNode(nodeId)) {
                graph.addNode(nodeId, {
                    type: 'file',
                    filePath: fileResult.filePath,
                    language: fileResult.language,
                    errorCount: fileResult.errors?.length ?? 0,
                    elementCount: fileResult.elements.length,
                    label: fileResult.filePath // Add label for visualization
                });
                fileNodeMap.set(fileResult.filePath, nodeId);
            } else {
                 // Update attributes if node somehow added twice (shouldn't happen with map)
                 graph.updateNodeAttribute(nodeId, 'errorCount', (count: number) => (count ?? 0) + (fileResult.errors?.length ?? 0));
                 graph.setNodeAttribute(nodeId, 'elementCount', fileResult.elements.length);
            }
        });

        // 2. Add edges based on imports
        fileResults.forEach(fileResult => {
            const sourceNodeId = fileNodeMap.get(fileResult.filePath);
            if (!sourceNodeId) {
                 console.warn(`Source file node not found in graph: ${fileResult.filePath}`);
                 return; // Skip if source file node doesn't exist
            }

            const importDetails = new Map<string, { count: number, names: string[] }>(); // targetNodeId -> import info

            fileResult.elements.forEach(element => {
                if (element.type === CodeElementType.Import) {
                    const importElement = element as ImportElement;
                    // Attempt to resolve the imported path relative to the current file
                    const targetPath = this.resolveImportPath(fileResult.filePath, importElement.source);

                    if (targetPath) {
                        const targetNodeId = fileNodeMap.get(targetPath);
                        if (targetNodeId && sourceNodeId !== targetNodeId) {
                             // Aggregate import details for the edge
                             if (!importDetails.has(targetNodeId)) {
                                importDetails.set(targetNodeId, { count: 0, names: [] });
                            }
                            const details = importDetails.get(targetNodeId)!;
                            details.count++;
                            // Fix checks for importedNames (array of objects)
                            let namesToAdd = importElement.importedNames;
                            if (importElement.isDefault && !namesToAdd.some(n => n.name === 'default')) {
                                namesToAdd.push({ name: 'default' });
                            }
                            if (importElement.isNamespace && !namesToAdd.some(n => n.name === '*')) {
                                namesToAdd.push({ name: '*' });
                            }
                            details.names.push(...namesToAdd.map(n => n.name));

                        } else if (!targetNodeId) {
                             // console.warn(`Resolved import "${importElement.source}" from "${fileResult.filePath}" to "${targetPath}", but target file was not analyzed or found in graph.`);
                        }
                    } else if (importElement.source && !importElement.source.startsWith('.')) {
                        // TODO: Handle external / node_module imports - maybe add nodes for packages?
                        // console.log(`Skipping non-relative import: ${importElement.source} in ${fileResult.filePath}`);
                    }
                }
            });

            // Add aggregated edges to the graph
             importDetails.forEach((details, targetNodeId) => {
                 const uniqueNames = Array.from(new Set(details.names.filter(n => n))); // Filter out empty names just in case
                 if (!graph.hasEdge(sourceNodeId, targetNodeId)) {
                     graph.addDirectedEdge(sourceNodeId, targetNodeId, {
                        type: 'imports',
                        count: details.count, // Number of import statements between these files
                        importedNames: uniqueNames, // List of unique things imported
                        label: `imports (${details.count})` // Add label for visualization
                    });
                 } else {
                     // If edge exists (shouldn't with multi:false), update attributes?
                     graph.updateDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'count', (c: number) => (c || 0) + details.count);
                     graph.updateDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'importedNames', (names: string[]) => Array.from(new Set([...(names || []), ...uniqueNames])));
                     graph.updateDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'label', (label: string) => `imports (${graph.getDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'count')})`);
                 }
             });
        });

        console.log(`Relations graph built: ${graph.order} nodes, ${graph.size} edges.`);
        return graph; // Return the graph instance
    }

    private summarizeResults(fileResults: FileAnalysisResult[]): { [key: string]: any } {
        const summary: { [key: string]: any } = {
            totalFilesAnalyzed: fileResults.length,
            filesWithErrors: fileResults.filter(f => f.errors && f.errors.length > 0).length,
            elementsByType: {},
            totalElements: 0,
            languages: {}
        };

        for (const fileResult of fileResults) {
             // Count languages
             if (fileResult.language) {
                 summary.languages[fileResult.language] = (summary.languages[fileResult.language] || 0) + 1;
             }

            for (const element of fileResult.elements) {
                summary.totalElements++;
                const typeName = element.type || 'Unknown'; // Use type directly
                summary.elementsByType[typeName] = (summary.elementsByType[typeName] || 0) + 1;
            }
        }

        console.log("Analysis Summary:", summary);
        return summary;
    }
}