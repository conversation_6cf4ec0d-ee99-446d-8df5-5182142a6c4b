/**
 * Plugin Registry - Self-Contained Plugin Architecture
 * 
 * Manages plugins organized by plugin (not by type).
 * Each plugin is self-contained with all its assets.
 */

import { AnalysisPlugin } from '../core/PluginSystem.js';

// Import self-contained plugins
import JavaScriptPlugin from './javascript-plugin/index.js';
import TypeScriptPlugin from './typescript-plugin/index.js';
import NestJSPlugin from './nestjs-plugin/index.js';
import ReactPlugin from './react-plugin/index.js';

/**
 * Plugin configuration for different project types
 */
export interface PluginConfig {
  name: string;
  description: string;
  plugins: string[];
  autoDetect?: (projectRoot: string) => Promise<boolean>;
}

/**
 * Available plugin configurations
 */
export const PLUGIN_CONFIGS: { [key: string]: PluginConfig } = {
  'nestjs-backend': {
    name: 'NestJS Backend',
    description: 'TypeScript + NestJS framework analysis',
    plugins: ['typescript-plugin', 'nestjs-plugin'],
    autoDetect: async (projectRoot: string) => {
      return await hasNestJSDependency(projectRoot);
    }
  },
  
  'react-frontend': {
    name: 'React Frontend',
    description: 'JavaScript/TypeScript + React framework analysis',
    plugins: ['javascript-plugin', 'typescript-plugin', 'react-plugin'],
    autoDetect: async (projectRoot: string) => {
      return await hasReactDependency(projectRoot);
    }
  },
  
  'typescript-project': {
    name: 'TypeScript Project',
    description: 'TypeScript language analysis',
    plugins: ['typescript-plugin'],
    autoDetect: async (projectRoot: string) => {
      return await hasTypeScriptConfig(projectRoot);
    }
  },
  
  'javascript-project': {
    name: 'JavaScript Project',
    description: 'JavaScript language analysis',
    plugins: ['javascript-plugin'],
    autoDetect: async (projectRoot: string) => {
      return true; // Default fallback
    }
  }
};

/**
 * Enhanced Plugin Registry for self-contained plugins
 */
export class PluginRegistry {
  private loadedPlugins: Map<string, AnalysisPlugin> = new Map();
  private currentConfig?: PluginConfig;
  
  /**
   * Auto-detect and load appropriate plugins
   */
  async autoDetectAndLoad(projectRoot: string): Promise<string> {
    // Try configurations in priority order
    const configNames = ['nestjs-backend', 'react-frontend', 'typescript-project', 'javascript-project'];
    
    for (const configName of configNames) {
      const config = PLUGIN_CONFIGS[configName];
      if (config.autoDetect && await config.autoDetect(projectRoot)) {
        await this.loadConfiguration(configName, projectRoot);
        return configName;
      }
    }
    
    // Fallback to JavaScript
    await this.loadConfiguration('javascript-project', projectRoot);
    return 'javascript-project';
  }
  
  /**
   * Load specific plugin configuration
   */
  async loadConfiguration(configName: string, projectRoot: string): Promise<void> {
    const config = PLUGIN_CONFIGS[configName];
    if (!config) {
      throw new Error(`Unknown configuration: ${configName}`);
    }
    
    this.currentConfig = config;
    
    // Load each plugin in the configuration
    for (const pluginName of config.plugins) {
      await this.loadPlugin(pluginName, projectRoot);
    }
    
    console.log(`✅ Loaded configuration: ${config.name}`);
    console.log(`   Plugins: ${config.plugins.join(', ')}`);
  }
  
  /**
   * Load individual plugin
   */
  private async loadPlugin(pluginName: string, projectRoot: string): Promise<void> {
    try {
      let PluginClass: any;
      
      // Load self-contained plugins
      switch (pluginName) {
        case 'javascript-plugin':
          PluginClass = JavaScriptPlugin;
          break;
        case 'typescript-plugin':
          PluginClass = TypeScriptPlugin;
          break;
        case 'nestjs-plugin':
          PluginClass = NestJSPlugin;
          break;
        case 'react-plugin':
          PluginClass = ReactPlugin;
          break;
        default:
          console.warn(`❌ Unknown plugin: ${pluginName}`);
          return;
      }
      
      // Create and initialize plugin
      const plugin = new PluginClass();
      await plugin.initialize(projectRoot);
      
      // Store loaded plugin
      this.loadedPlugins.set(pluginName, plugin);
      
      console.log(`   ✅ ${pluginName}`);
      
    } catch (error) {
      console.warn(`   ❌ Failed to load ${pluginName}:`, error);
    }
  }
  
  /**
   * Get plugins that can handle a specific file
   */
  getPluginsForFile(filePath: string, language: string, framework?: string): AnalysisPlugin[] {
    return Array.from(this.loadedPlugins.values())
      .filter(plugin => plugin.canHandle(filePath, language, framework))
      .sort((a, b) => b.metadata.priority - a.metadata.priority);
  }
  
  /**
   * Get all loaded plugins
   */
  getAllPlugins(): AnalysisPlugin[] {
    return Array.from(this.loadedPlugins.values());
  }
  
  /**
   * Get current configuration
   */
  getCurrentConfig(): PluginConfig | undefined {
    return this.currentConfig;
  }
  
  /**
   * List available configurations
   */
  getAvailableConfigs(): PluginConfig[] {
    return Object.values(PLUGIN_CONFIGS);
  }
  
  /**
   * Cleanup all plugins
   */
  async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.loadedPlugins.values()).map(plugin => 
      plugin.cleanup().catch(error => {
        console.warn(`Failed to cleanup plugin ${plugin.metadata.name}:`, error);
      })
    );
    
    await Promise.all(cleanupPromises);
    this.loadedPlugins.clear();
  }
}

// Helper functions for auto-detection

async function hasNestJSDependency(projectRoot: string): Promise<boolean> {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const packageJsonPath = path.join(projectRoot, 'package.json');
    const packageContent = await fs.readFile(packageJsonPath, 'utf-8');
    const packageJson = JSON.parse(packageContent);
    
    const dependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };
    
    return !!(dependencies['@nestjs/core'] || dependencies['@nestjs/common']);
  } catch {
    return false;
  }
}

async function hasReactDependency(projectRoot: string): Promise<boolean> {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const packageJsonPath = path.join(projectRoot, 'package.json');
    const packageContent = await fs.readFile(packageJsonPath, 'utf-8');
    const packageJson = JSON.parse(packageContent);
    
    const dependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };
    
    return !!(dependencies['react'] || dependencies['@types/react']);
  } catch {
    return false;
  }
}

async function hasTypeScriptConfig(projectRoot: string): Promise<boolean> {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const tsconfigPath = path.join(projectRoot, 'tsconfig.json');
    await fs.access(tsconfigPath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Create plugin registry with auto-detection
 */
export async function createPluginRegistry(projectRoot: string): Promise<PluginRegistry> {
  const registry = new PluginRegistry();
  const detectedConfig = await registry.autoDetectAndLoad(projectRoot);
  console.log(`🎯 Auto-detected configuration: ${detectedConfig}`);
  return registry;
}

/**
 * Create plugin registry with specific configuration
 */
export async function createPluginRegistryWithConfig(
  configName: string, 
  projectRoot: string
): Promise<PluginRegistry> {
  const registry = new PluginRegistry();
  await registry.loadConfiguration(configName, projectRoot);
  return registry;
}
