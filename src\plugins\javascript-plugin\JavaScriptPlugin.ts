/**
 * JavaScript Plugin - Basic Implementation
 * 
 * Self-contained JavaScript analysis plugin using tree-sitter.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginAnalysisContext, 
  PluginAnalysisResult 
} from '../../core/PluginSystem.js';
import { CodeElement, CodeElementType } from '../../types/CodeElement.js';
// TODO: Re-enable tree-sitter integration with plugin-specific queries
// import { TreeSitterParser } from '../../parsers/TreeSitterParser.js';

export class JavaScriptPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'javascript-plugin',
    version: '1.0.0',
    description: 'JavaScript syntax analysis using tree-sitter',
    author: 'Cyzer Team',
    
    languages: ['javascript'],
    frameworks: [],
    fileExtensions: ['.js', '.mjs', '.cjs'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: false,
      crossFileAnalysis: true,
      typeInference: false,
      dependencyTracking: true,
      callGraphGeneration: true,
      frameworkPatterns: false,
      decoratorAnalysis: false,
      componentAnalysis: false,
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 100,
    dependencies: [],
    
    requiresTypeScript: false,
    requiresNodeModules: false,
    memoryIntensive: false
  };
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    // Basic implementation - no tree-sitter setup needed for now
  }

  async cleanup(): Promise<void> {
    // No cleanup needed for basic implementation
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    return language === 'javascript' && 
           this.metadata.fileExtensions.some(ext => filePath.endsWith(ext));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();

    try {
      // For now, use simple pattern matching instead of tree-sitter
      // TODO: Integrate tree-sitter with plugin-specific queries
      const elements = this.analyzeJavaScriptBasic(context);

      // Share data with other plugins
      const sharedData = new Map<string, any>();
      sharedData.set('javascript-ast', { elements }); // Mock AST structure
      sharedData.set('javascript-elements', elements);

      return {
        elements,
        errors: [],
        warnings: [],
        metadata: {
          language: 'javascript',
          parser: 'basic-pattern-matching',
          totalElements: elements.length
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0.85, // Lower confidence for basic implementation
        sharedData
      };

    } catch (error) {
      return {
        elements: [],
        errors: [`JavaScript analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Basic JavaScript analysis using pattern matching
   */
  private analyzeJavaScriptBasic(context: PluginAnalysisContext): CodeElement[] {
    const elements: CodeElement[] = [];
    const lines = context.fileContent.split('\n');

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // Function declarations
      const functionMatch = trimmedLine.match(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/);
      if (functionMatch) {
        elements.push({
          name: functionMatch[1],
          type: CodeElementType.Function,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: trimmedLine.includes('export'),
          fullText: trimmedLine,
          metadata: {
            isAsync: trimmedLine.includes('async'),
            isArrowFunction: false
          }
        });
      }

      // Arrow functions
      const arrowMatch = trimmedLine.match(/(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*.*=>/);
      if (arrowMatch) {
        elements.push({
          name: arrowMatch[1],
          type: CodeElementType.Function,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: trimmedLine.includes('export'),
          fullText: trimmedLine,
          metadata: {
            isAsync: trimmedLine.includes('async'),
            isArrowFunction: true
          }
        });
      }

      // Class declarations
      const classMatch = trimmedLine.match(/class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/);
      if (classMatch) {
        elements.push({
          name: classMatch[1],
          type: CodeElementType.Class,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: trimmedLine.includes('export'),
          fullText: trimmedLine
        });
      }

      // Variable declarations
      const varMatch = trimmedLine.match(/(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/);
      if (varMatch && !arrowMatch) { // Don't double-count arrow functions
        elements.push({
          name: varMatch[1],
          type: CodeElementType.Variable,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: trimmedLine.includes('export'),
          fullText: trimmedLine,
          metadata: {
            isConst: trimmedLine.startsWith('const'),
            isLet: trimmedLine.startsWith('let')
          }
        });
      }

      // Import statements
      const importMatch = trimmedLine.match(/import\s+.*from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        elements.push({
          name: 'import',
          type: CodeElementType.Import,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: false,
          fullText: trimmedLine,
          metadata: {
            source: importMatch[1],
            isESModule: true
          }
        });
      }

      // CommonJS require
      const requireMatch = trimmedLine.match(/require\s*\(\s*['"]([^'"]+)['"]\s*\)/);
      if (requireMatch) {
        elements.push({
          name: 'require',
          type: CodeElementType.Import,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: false,
          fullText: trimmedLine,
          metadata: {
            source: requireMatch[1],
            isCommonJS: true
          }
        });
      }

      // Export statements
      if (trimmedLine.startsWith('module.exports') || trimmedLine.startsWith('exports.')) {
        elements.push({
          name: 'export',
          type: CodeElementType.Export,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: true,
          fullText: trimmedLine,
          metadata: {
            isCommonJS: true
          }
        });
      }

      // ES6 Export statements
      if (trimmedLine.startsWith('export ')) {
        let exportName = 'export';

        // Extract export name for different patterns
        if (trimmedLine.includes('export {')) {
          // Named exports: export { name1, name2 }
          const match = trimmedLine.match(/export\s*\{\s*([^}]+)\s*\}/);
          if (match) {
            exportName = match[1].split(',')[0].trim(); // First exported name
          }
        } else if (trimmedLine.includes('export default')) {
          // Default export: export default Something
          exportName = 'default';
        } else if (trimmedLine.includes('export const') || trimmedLine.includes('export let') || trimmedLine.includes('export var')) {
          // Export declaration: export const name = ...
          const match = trimmedLine.match(/export\s+(const|let|var)\s+(\w+)/);
          if (match) {
            exportName = match[2];
          }
        } else if (trimmedLine.includes('export function')) {
          // Export function: export function name() {}
          const match = trimmedLine.match(/export\s+function\s+(\w+)/);
          if (match) {
            exportName = match[1];
          }
        } else if (trimmedLine.includes('export class')) {
          // Export class: export class Name {}
          const match = trimmedLine.match(/export\s+class\s+(\w+)/);
          if (match) {
            exportName = match[1];
          }
        }

        elements.push({
          name: exportName,
          type: CodeElementType.Export,
          filePath: context.filePath,
          location: {
            startLine: index + 1,
            endLine: index + 1,
            startCol: 0,
            endCol: line.length,
            startPos: 0,
            endPos: line.length
          },
          isExported: true,
          fullText: trimmedLine,
          metadata: {
            isESModule: true
          }
        });
      }
    });

    return elements;
  }

  /**
   * Enhance elements with JavaScript-specific information
   */
  private enhanceElements(elements: CodeElement[], context: PluginAnalysisContext): CodeElement[] {
    return elements.map(element => {
      const enhanced = { ...element };
      
      // Add JavaScript-specific metadata
      if (element.type === CodeElementType.Function) {
        enhanced.metadata = {
          ...enhanced.metadata,
          isAsync: this.isAsyncFunction(element),
          isArrowFunction: this.isArrowFunction(element),
          isCallback: this.isCallbackFunction(element)
        };
      }
      
      if (element.type === CodeElementType.Variable) {
        enhanced.metadata = {
          ...enhanced.metadata,
          isConst: this.isConstVariable(element),
          isLet: this.isLetVariable(element),
          isGlobal: this.isGlobalVariable(element)
        };
      }
      
      if (element.type === CodeElementType.Import) {
        enhanced.metadata = {
          ...enhanced.metadata,
          isESModule: this.isESModuleImport(element),
          isCommonJS: this.isCommonJSImport(element)
        };
      }
      
      return enhanced;
    });
  }
  
  // Pattern detection methods
  private isAsyncFunction(element: CodeElement): boolean {
    return element.fullText?.includes('async ') || false;
  }
  
  private isArrowFunction(element: CodeElement): boolean {
    return element.fullText?.includes('=>') || false;
  }
  
  private isCallbackFunction(element: CodeElement): boolean {
    // Simple heuristic - function passed as argument
    return element.name.toLowerCase().includes('callback') ||
           element.name.toLowerCase().includes('handler');
  }
  
  private isConstVariable(element: CodeElement): boolean {
    return element.fullText?.startsWith('const ') || false;
  }
  
  private isLetVariable(element: CodeElement): boolean {
    return element.fullText?.startsWith('let ') || false;
  }
  
  private isGlobalVariable(element: CodeElement): boolean {
    // Simple heuristic - top-level variable
    return element.location.startLine <= 10;
  }
  
  private isESModuleImport(element: CodeElement): boolean {
    return element.fullText?.startsWith('import ') || false;
  }
  
  private isCommonJSImport(element: CodeElement): boolean {
    return element.fullText?.includes('require(') || false;
  }
}
