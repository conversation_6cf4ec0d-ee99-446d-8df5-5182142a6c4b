/**
 * JavaScript Plugin Tests
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { JavaScriptPlugin } from '../JavaScriptPlugin.js';
import { PluginAnalysisContext } from '../../../core/PluginSystem.js';
import { CodeElementType } from '../../../types/CodeElement.js';
import * as fs from 'fs/promises';
import * as path from 'path';

describe('JavaScriptPlugin', () => {
  let plugin: JavaScriptPlugin;
  let testFileContent: string;

  beforeEach(async () => {
    plugin = new JavaScriptPlugin();
    await plugin.initialize('./src/plugins/javascript-plugin/tests/fixtures');

    // Load test file from plugin's own fixtures
    const testFilePath = path.join(__dirname, 'fixtures', 'basic.js');
    testFileContent = await fs.readFile(testFilePath, 'utf-8');
  });

  afterEach(async () => {
    await plugin.cleanup();
  });

  it('should initialize successfully', () => {
    expect(plugin.metadata.name).toBe('javascript-plugin');
    expect(plugin.metadata.languages).toContain('javascript');
  });

  it('should handle JavaScript files', () => {
    expect(plugin.canHandle('test.js', 'javascript')).toBe(true);
    expect(plugin.canHandle('test.mjs', 'javascript')).toBe(true);
    expect(plugin.canHandle('test.ts', 'javascript')).toBe(false);
    expect(plugin.canHandle('test.py', 'javascript')).toBe(false);
  });

  it('should analyze JavaScript file and extract elements', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);

    expect(result.errors).toHaveLength(0);
    expect(result.elements.length).toBeGreaterThan(0);
    expect(result.metadata.language).toBe('javascript');
    expect(result.confidence).toBeGreaterThan(0.8);
  });

  it('should detect functions', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);
    const functions = result.elements.filter(e => e.type === CodeElementType.Function);

    expect(functions.length).toBeGreaterThan(0);

    // Should find calculateSum function
    const calculateSum = functions.find(f => f.name === 'calculateSum');
    expect(calculateSum).toBeDefined();
    expect(calculateSum?.metadata?.isAsync).toBe(false);
  });

  it('should detect async functions', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);
    const functions = result.elements.filter(e => e.type === CodeElementType.Function);

    // Should find fetchData async function
    const fetchData = functions.find(f => f.name === 'fetchData');
    expect(fetchData).toBeDefined();
    expect(fetchData?.metadata?.isAsync).toBe(true);
  });

  it('should detect arrow functions', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);
    const functions = result.elements.filter(e => e.type === CodeElementType.Function);

    // Should find multiply arrow function
    const multiply = functions.find(f => f.name === 'multiply');
    expect(multiply).toBeDefined();
    expect(multiply?.metadata?.isArrowFunction).toBe(true);
  });

  it('should detect classes', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);
    const classes = result.elements.filter(e => e.type === CodeElementType.Class);

    expect(classes.length).toBeGreaterThan(0);

    // Should find Calculator class
    const calculator = classes.find(c => c.name === 'Calculator');
    expect(calculator).toBeDefined();
  });

  it('should detect variables', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);
    const variables = result.elements.filter(e => e.type === CodeElementType.Variable);

    expect(variables.length).toBeGreaterThan(0);
  });

  it('should detect imports/exports', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);
    const imports = result.elements.filter(e => e.type === CodeElementType.Import);
    const exports = result.elements.filter(e => e.type === CodeElementType.Export);

    // Should have ES6 exports
    expect(exports.length).toBeGreaterThan(0);
  });

  it('should share data with other plugins', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'fixtures/basic.js',
      fileContent: testFileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);

    expect(result.sharedData).toBeDefined();
    expect(result.sharedData?.has('javascript-ast')).toBe(true);
    expect(result.sharedData?.has('javascript-elements')).toBe(true);
  });

  it('should handle empty file gracefully', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'empty.js',
      fileContent: '',
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);

    expect(result.errors).toHaveLength(0);
    expect(result.elements).toHaveLength(0);
    expect(result.confidence).toBeGreaterThan(0);
  });

  it('should handle malformed JavaScript gracefully', async () => {
    const context: PluginAnalysisContext = {
      filePath: 'malformed.js',
      fileContent: 'function incomplete( {',
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };

    const result = await plugin.analyze(context);

    // Should not crash, might have errors but should return a result
    expect(result).toBeDefined();
    expect(result.confidence).toBeDefined();
  });
});
