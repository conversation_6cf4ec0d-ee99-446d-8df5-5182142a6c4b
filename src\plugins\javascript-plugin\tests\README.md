# JavaScript Plugin Tests

## Test Overview
Comprehensive tests for pure JavaScript syntax analysis, excluding runtime-specific APIs.

## Test Categories

### 1. Basic Syntax Tests
**File**: `JavaScriptPlugin.test.ts`
**Focus**: Core JavaScript language constructs

#### Function Detection
- Regular function declarations
- Function expressions
- Arrow functions (single param, multiple params, no params)
- Async functions
- Generator functions
- Method definitions in objects/classes

#### Class Detection  
- Class declarations
- Constructor methods
- Instance methods
- Static methods
- Getter/setter methods
- Class expressions

#### Variable Detection
- `const` declarations
- `let` declarations  
- `var` declarations
- Destructuring assignments
- Default parameters

### 2. ES6+ Features Tests
**Focus**: Modern JavaScript syntax

#### Module System
- `import` statements (named, default, namespace)
- `export` statements (named, default, re-exports)
- Dynamic imports (when supported)

#### Advanced Syntax
- Template literals
- Spread operator
- Rest parameters
- Object/array destructuring
- Computed property names

### 3. Edge Cases Tests
**Focus**: Complex scenarios and error handling

#### Nested Structures
- Functions within functions
- Classes within modules
- Complex object literals
- Nested destructuring

#### Error Handling
- Malformed syntax
- Incomplete code
- Mixed syntax patterns

## Test Assets

### Pure JavaScript Files
**Location**: `tests/fixtures/javascript/pure-js/`

#### `basic.js`
```javascript
// Basic function and class examples
function calculateSum(a, b) {
    return a + b;
}

const multiply = (x, y) => x * y;

class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        return a + b;
    }
}

export { Calculator, calculateSum };
```

#### `es6-features.js`
```javascript
// Modern JavaScript features
const { name, age } = person;
const numbers = [1, 2, 3];
const doubled = numbers.map(n => n * 2);

// Template literals
const message = `Hello ${name}, you are ${age} years old`;

// Async/await
async function fetchData() {
    const response = await fetch('/api/data');
    return response.json();
}

// Generators
function* fibonacci() {
    let a = 0, b = 1;
    while (true) {
        yield a;
        [a, b] = [b, a + b];
    }
}
```

### What NOT to Include
**These patterns should be in other plugin tests:**

#### Node.js Specific (nodejs-plugin tests)
```javascript
// DON'T include in JavaScript plugin tests
const fs = require('fs');
module.exports = something;
process.env.NODE_ENV;
__dirname;
```

#### Framework Specific (framework plugin tests)
```javascript
// DON'T include in JavaScript plugin tests
import React from 'react';
app.get('/route', handler);
@Component decorator;
```

## Test Execution

### Running Tests
```bash
# Run only JavaScript plugin tests
npm test src/plugins/javascript-plugin/tests/

# Run with coverage
npm test -- --coverage src/plugins/javascript-plugin/
```

### Test Structure
Each test follows this pattern:
1. **Setup**: Create plugin instance
2. **Load**: Read test file content
3. **Analyze**: Run plugin analysis
4. **Assert**: Verify detected elements
5. **Cleanup**: Clean up resources

### Expected Results
For `basic.js`, the plugin should detect:
- 3 functions: `calculateSum`, `multiply`, `add`
- 1 class: `Calculator`
- 1 constructor: `Calculator.constructor`
- 2 exports: `Calculator`, `calculateSum`
- Various variables and parameters

## Test Data Guidelines

### Creating New Test Files
1. **Focus on pure JavaScript** - no runtime APIs
2. **Include comments** explaining what should be detected
3. **Use realistic patterns** from actual codebases
4. **Test edge cases** like nested structures
5. **Keep files focused** on specific features

### File Naming Convention
- `basic.js` - Core language features
- `es6-advanced.js` - Modern JavaScript features
- `edge-cases.js` - Complex scenarios
- `malformed.js` - Error handling tests

### Documentation Requirements
Each test file should have:
- Header comment explaining purpose
- Inline comments for expected detections
- Clear separation of different feature areas

## Integration with Other Plugins

### TypeScript Plugin Tests
- Should inherit JavaScript detection capabilities
- Should add TypeScript-specific elements on top
- Should not duplicate JavaScript-only tests

### Framework Plugin Tests  
- Should use JavaScript plugin as foundation
- Should override with framework-specific patterns
- Should test interaction between base and framework features

## Maintenance Notes

### When to Update Tests
- New JavaScript language features (ES2024+)
- Bug fixes in detection logic
- Changes to plugin architecture
- New edge cases discovered

### Test Performance
- Keep test files reasonably sized (< 1000 lines)
- Use focused tests rather than large comprehensive files
- Mock external dependencies
- Optimize for fast feedback during development

## Recommended Test Repositories

### **Small Projects** (< 10k lines)
Perfect for validating core JavaScript detection without complexity:

#### **lodash** (~5k lines)
```bash
git clone https://github.com/lodash/lodash.git test-repositories/lodash
```
**Why**: Pure JavaScript utility library with comprehensive function patterns
- **Functions**: Hundreds of utility functions (map, filter, reduce, etc.)
- **Modules**: Clean ES6 import/export patterns
- **Patterns**: Functional programming patterns, currying, composition
- **Expected Elements**: ~300 functions, ~50 modules, minimal classes

#### **clsx** (~200 lines)
```bash
git clone https://github.com/lukeed/clsx.git test-repositories/clsx
```
**Why**: Minimal JavaScript library, perfect for testing basic detection
- **Functions**: Simple utility functions
- **ES6**: Modern JavaScript syntax
- **Expected Elements**: ~5 functions, 1 main export

### **Medium Projects** (10k-100k lines)
For testing JavaScript analysis at scale:

#### **Express.js** (~15k lines)
```bash
git clone https://github.com/expressjs/express.git test-repositories/express
```
**Why**: Popular Node.js framework with complex JavaScript patterns
- **Functions**: Route handlers, middleware, utilities
- **Classes**: Application, Router, Request, Response classes
- **Modules**: Complex module system with circular dependencies
- **Expected Elements**: ~200 functions, ~20 classes, ~50 modules

#### **Axios** (~8k lines)
```bash
git clone https://github.com/axios/axios.git test-repositories/axios
```
**Why**: HTTP client library with modern JavaScript patterns
- **Functions**: Request/response handling, interceptors
- **Classes**: Axios class, adapters, transformers
- **Async**: Promise-based API, async/await patterns
- **Expected Elements**: ~100 functions, ~15 classes, ~30 modules

### **Large Projects** (100k+ lines)
For stress testing and performance validation:

#### **React** (~200k lines)
```bash
git clone https://github.com/facebook/react.git test-repositories/react
```
**Why**: Complex JavaScript codebase with advanced patterns
- **Functions**: Thousands of React internals functions
- **Classes**: Component classes, fiber architecture
- **JSX**: Extensive JSX usage (future enhancement)
- **Expected Elements**: ~2000 functions, ~100 classes, ~200 modules

### **Test Execution Commands**

```bash
# Test on small JavaScript project
npm run test:real-repos -- --project lodash --config javascript-project

# Test on medium project with performance metrics
npm run test:real-repos -- --project express --config javascript-project --performance

# Test on large project (stress test)
npm run test:real-repos -- --project react --config javascript-project --timeout 300

# Compare detection accuracy across projects
npm run test:real-repos -- --compare lodash,axios,express
```
