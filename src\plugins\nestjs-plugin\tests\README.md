# NestJS Plugin Tests

## Overview
Comprehensive tests for NestJS framework-specific patterns, building upon TypeScript and JavaScript capabilities.

## Plugin Scope
**NestJS Plugin** analyzes NestJS framework patterns while inheriting all TypeScript and JavaScript capabilities.

### ✅ What This Plugin Detects
- **All TypeScript features** (inherited from typescript-plugin)
- **All JavaScript features** (inherited via typescript-plugin)
- **Controllers** - @Controller decorated classes
- **Services** - @Injectable decorated classes
- **Modules** - @Module decorated classes
- **HTTP Endpoints** - @Get, @Post, @Put, @Delete, @Patch methods
- **Guards** - @UseGuards, CanActivate implementations
- **Interceptors** - @UseInterceptors, NestInterceptor implementations
- **Pipes** - @UsePipes, PipeTransform implementations
- **Dependency Injection** - Constructor injection patterns
- **Parameter Decorators** - @Param, @Body, @Query, @Headers
- **Custom Decorators** - Framework-specific decorator patterns

### ❌ What This Plugin Does NOT Detect
- **Express-specific patterns** (use express-plugin)
- **Node.js APIs** (use nodejs-plugin)
- **Database patterns** (use database-specific plugins)
- **Testing patterns** (use testing-specific plugins)

## Test Structure

### Test Categories

#### 1. Core NestJS Components
**Focus**: Essential NestJS building blocks

- **Controllers**: Route handling, HTTP methods, parameter extraction
- **Services**: Business logic, dependency injection
- **Modules**: Application structure, imports/exports
- **Providers**: Custom providers, factory providers

#### 2. HTTP Layer
**Focus**: REST API patterns

- **Endpoints**: HTTP method decorators, route parameters
- **Request Handling**: Body, query, param decorators
- **Response Handling**: Status codes, headers, custom responses
- **Middleware Integration**: Guards, interceptors, pipes

#### 3. Dependency Injection
**Focus**: DI container patterns

- **Constructor Injection**: Service dependencies
- **Custom Providers**: Factory, value, class providers
- **Injection Tokens**: Custom injection tokens
- **Optional Dependencies**: @Optional decorator

#### 4. Architecture Patterns
**Focus**: NestJS architectural concepts

- **Module Organization**: Feature modules, shared modules
- **Cross-Cutting Concerns**: Guards, interceptors, filters
- **Configuration**: Environment variables, config modules
- **Exception Handling**: Exception filters, HTTP exceptions

## Test Assets

### Self-Contained Fixtures
**Location**: `src/plugins/nestjs-plugin/tests/fixtures/`

#### `controller.ts` - Comprehensive NestJS Patterns
```typescript
import {
    Controller, Get, Post, Put, Delete,
    Body, Param, Query, UseGuards, UseInterceptors,
    Injectable, CanActivate, NestInterceptor, Module
} from '@nestjs/common';

// Controllers
@Controller('users')
@UseGuards(AuthGuard)
export class UsersController {
    constructor(private readonly usersService: UsersService) {}
    
    @Get()
    async findAll(@Query('page') page: number = 1) {
        return this.usersService.findAll({ page });
    }
    
    @Get(':id')
    async findOne(@Param('id') id: number) {
        return this.usersService.findById(id);
    }
    
    @Post()
    async create(@Body() createUserDto: CreateUserDto) {
        return this.usersService.create(createUserDto);
    }
}

// Services
@Injectable()
export class UsersService {
    constructor(
        private readonly database: any,
        private readonly emailService: any
    ) {}
    
    async findAll(options: any) {
        return this.database.users.findMany(options);
    }
}

// Guards
@Injectable()
class AuthGuard implements CanActivate {
    canActivate(context: any): boolean {
        return true;
    }
}

// Modules
@Module({
    controllers: [UsersController],
    providers: [UsersService, AuthGuard],
    exports: [UsersService]
})
export class UsersModule {}
```

### Expected Detections
For `controller.ts`, the plugin should detect:

#### NestJS Components
- **Controllers**: `UsersController`, `ProductsController`
- **Services**: `UsersService` (with @Injectable)
- **Guards**: `AuthGuard`, `RolesGuard`
- **Interceptors**: `LoggingInterceptor`
- **Pipes**: `ValidationPipe`
- **Modules**: `UsersModule`

#### HTTP Endpoints
- **GET Endpoints**: `/users`, `/users/:id`, `/products`
- **POST Endpoints**: `/users`, `/products`
- **PUT Endpoints**: `/users/:id`
- **DELETE Endpoints**: `/users/:id`

#### Decorators
- **Class Decorators**: `@Controller`, `@Injectable`, `@Module`
- **Method Decorators**: `@Get`, `@Post`, `@UseGuards`
- **Parameter Decorators**: `@Param`, `@Body`, `@Query`

#### Architecture Patterns
- **Dependency Injection**: Constructor parameters
- **Route Parameters**: Path parameters, query parameters
- **Guards Usage**: Method and class-level guards
- **Module Structure**: Controllers, providers, exports

## Test Execution

### Running Tests
```bash
# Run only NestJS plugin tests
npm test src/plugins/nestjs-plugin/tests/

# Run with coverage
npm test -- --coverage src/plugins/nestjs-plugin/

# Run specific test category
npm test -- --grep "should detect controllers"
```

### Test Dependencies
- **Inherits from TypeScript Plugin**: All TypeScript detection capabilities
- **Inherits from JavaScript Plugin**: All JavaScript detection capabilities
- **NestJS Framework**: Framework-specific patterns and decorators

## Integration with Other Plugins

### Plugin Dependencies
```
nestjs-plugin
├── depends on: typescript-plugin (required)
├── can use: express-plugin (optional - if Express detected)
├── can use: fastify-plugin (optional - if Fastify detected)
└── can use: nodejs-plugin (optional - if Node.js APIs detected)
```

### Framework Integration
- **Express Plugin**: Can enhance NestJS analysis when Express is detected
- **Node.js Plugin**: Provides runtime API detection
- **Database Plugins**: Could integrate with TypeORM, Mongoose patterns

### Shared Data
The plugin provides shared data for other plugins:
- `nestjs-architecture`: Application structure metadata
- `nestjs-endpoints`: API endpoint information
- `nestjs-dependencies`: Dependency injection graph
- `nestjs-modules`: Module organization data

## Plugin Intersections

### Multi-Plugin Analysis
A single NestJS file can be analyzed by multiple plugins:

```typescript
// This file would be analyzed by ALL these plugins:

import { Controller, Get } from '@nestjs/common';  // nestjs-plugin
import { Request, Response } from 'express';       // express-plugin
import * as fs from 'fs';                          // nodejs-plugin

@Controller('files')                               // nestjs-plugin
export class FilesController {                     // typescript-plugin
    @Get()                                         // nestjs-plugin
    async getFiles(                                // typescript-plugin
        @Req() req: Request,                       // nestjs-plugin + express-plugin
        @Res() res: Response                       // nestjs-plugin + express-plugin
    ): Promise<string[]> {                         // typescript-plugin
        return fs.readdirSync('./uploads');        // nodejs-plugin
    }
}
```

Each plugin contributes its domain expertise to the overall analysis.

## Future Enhancements

### High Priority
- [ ] **GraphQL Integration** - Detect @Resolver, @Query, @Mutation patterns
- [ ] **Microservices Patterns** - @MessagePattern, @EventPattern detection
- [ ] **WebSocket Support** - @WebSocketGateway, @SubscribeMessage patterns
- [ ] **Testing Patterns** - @Test, @Mock, testing utilities

### Medium Priority
- [ ] **Configuration Analysis** - @ConfigService, environment variables
- [ ] **Database Integration** - TypeORM, Mongoose pattern detection
- [ ] **Validation Patterns** - class-validator, DTO validation
- [ ] **Authentication Patterns** - Passport, JWT strategies

### Low Priority
- [ ] **Performance Monitoring** - Built-in metrics, health checks
- [ ] **Documentation Generation** - Swagger/OpenAPI integration
- [ ] **Code Generation** - CLI command analysis
- [ ] **Custom Decorator Support** - User-defined decorator patterns

## Framework Evolution

### NestJS Version Support
- **Current**: NestJS 8.x, 9.x, 10.x patterns
- **Future**: Track new decorator patterns, framework features
- **Backward Compatibility**: Support older NestJS versions

### Decorator Syntax Changes
- Monitor TypeScript decorator proposal changes
- Update patterns when decorators become stable
- Maintain compatibility with experimental decorators

## Maintenance Guidelines

### When to Update Tests
- New NestJS framework versions
- New decorator patterns introduced
- Changes to dependency injection patterns
- Integration with new NestJS modules

### Test Asset Guidelines
1. **Focus on NestJS-specific patterns** - Don't duplicate TypeScript tests
2. **Use realistic application patterns** - Based on actual NestJS apps
3. **Test framework integration** - Guards, interceptors, pipes together
4. **Document architectural patterns** - Clear module organization
5. **Keep current with framework** - Update for new NestJS features

### Performance Considerations
- NestJS applications can be large - optimize for performance
- Cache decorator analysis results
- Use incremental analysis for large codebases
- Consider memory usage with complex dependency graphs

## Recommended Test Repositories

### **Small Projects** (< 5k lines)
Perfect for validating core NestJS patterns:

#### **NestJS Sample Apps** (~2k lines each)
```bash
git clone https://github.com/nestjs/nest.git test-repositories/nestjs-samples
cd test-repositories/nestjs-samples/sample
```
**Why**: Official NestJS examples with clean patterns
- **Controllers**: Basic CRUD operations with decorators
- **Services**: Dependency injection patterns
- **Modules**: Clean module organization
- **Expected Elements**: ~10 controllers, ~15 services, ~20 modules

#### **NestJS GraphQL** (~3k lines)
```bash
git clone https://github.com/nestjs/graphql.git test-repositories/nestjs-graphql
```
**Why**: GraphQL integration patterns
- **Resolvers**: @Resolver, @Query, @Mutation decorators
- **Types**: GraphQL type definitions
- **Guards**: Authentication and authorization patterns
- **Expected Elements**: ~8 resolvers, ~12 types, ~5 guards

### **Medium Projects** (5k-50k lines)
For testing NestJS analysis at scale:

#### **NestJS Realworld Example** (~15k lines)
```bash
git clone https://github.com/lujakob/nestjs-realworld-example-app.git test-repositories/nestjs-realworld
```
**Why**: Complete application with authentication, CRUD, etc.
- **Controllers**: User, Article, Profile controllers
- **Services**: Business logic with dependency injection
- **Guards**: JWT authentication, role-based access
- **Modules**: Feature-based module organization
- **Expected Elements**: ~15 controllers, ~20 services, ~10 guards, ~25 modules

#### **NestJS Microservices** (~25k lines)
```bash
git clone https://github.com/nestjs/nest.git test-repositories/nestjs-microservices
cd test-repositories/nestjs-microservices/sample/23-microservice-grpc
```
**Why**: Microservice patterns and message handling
- **Message Patterns**: @MessagePattern, @EventPattern
- **Microservice Controllers**: gRPC, TCP, Redis patterns
- **Client Integration**: Microservice client patterns
- **Expected Elements**: ~20 message patterns, ~10 controllers, ~15 services

### **Large Projects** (50k+ lines)
For stress testing NestJS analysis:

#### **Medusa** (~100k lines)
```bash
git clone https://github.com/medusajs/medusa.git test-repositories/medusa
```
**Why**: Large-scale e-commerce platform built with NestJS-like patterns
- **Controllers**: Extensive REST API controllers
- **Services**: Complex business logic services
- **Middleware**: Custom middleware and interceptors
- **Database**: TypeORM integration patterns
- **Expected Elements**: ~100 controllers, ~150 services, ~50 modules

#### **Amplication** (~200k lines)
```bash
git clone https://github.com/amplication/amplication.git test-repositories/amplication
```
**Why**: Code generation platform with complex NestJS architecture
- **Generated Code**: Auto-generated NestJS patterns
- **Complex Decorators**: Custom decorator implementations
- **Module System**: Large-scale module organization
- **Expected Elements**: ~200 controllers, ~300 services, ~100 modules

### **Test Execution Commands**

```bash
# Test on small NestJS project
npm run test:real-repos -- --project nestjs-samples --config nestjs-backend

# Test decorator detection on medium project
npm run test:real-repos -- --project nestjs-realworld --config nestjs-backend --focus decorators

# Test large project with performance metrics
npm run test:real-repos -- --project medusa --config nestjs-backend --performance --timeout 900

# Compare NestJS vs TypeScript detection
npm run test:real-repos -- --project nestjs-realworld --compare nestjs-plugin,typescript-plugin
```

### **Expected Performance Metrics**

#### **Small Projects** (< 5k lines)
- **Analysis Time**: < 2 seconds
- **Memory Usage**: < 100MB
- **Accuracy**: > 98% (excellent on clean NestJS patterns)
- **NestJS Elements**: Controllers, services, modules, decorators

#### **Medium Projects** (5k-50k lines)
- **Analysis Time**: < 25 seconds
- **Memory Usage**: < 400MB
- **Accuracy**: > 95% (very good on structured applications)
- **Complex Patterns**: Guards, interceptors, pipes, middleware

#### **Large Projects** (50k+ lines)
- **Analysis Time**: < 120 seconds
- **Memory Usage**: < 2GB
- **Accuracy**: > 90% (good accuracy on enterprise applications)
- **Enterprise Patterns**: Microservices, complex DI, custom decorators

### **Common Patterns to Test**
- **CRUD Controllers** - Standard REST operations
- **Authentication** - Guards, strategies, decorators
- **File Upload** - Multer integration, file handling
- **Real-time Features** - WebSockets, Server-Sent Events
- **Microservices** - Message patterns, event handling
