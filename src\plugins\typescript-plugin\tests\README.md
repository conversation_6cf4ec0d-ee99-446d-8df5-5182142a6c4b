# TypeScript Plugin Tests

## Overview
Comprehensive tests for TypeScript-specific syntax analysis, building upon JavaScript capabilities with type system features.

## Plugin Scope
**TypeScript Plugin** analyzes TypeScript-specific language features while inheriting all JavaScript capabilities from the JavaScript plugin.

### ✅ What This Plugin Detects
- **All JavaScript features** (inherited from javascript-plugin)
- **Type annotations** - function parameters, return types, variable types
- **Interfaces** - interface declarations and implementations
- **Type aliases** - custom type definitions
- **Enums** - numeric and string enums
- **Generics** - type parameters in functions, classes, interfaces
- **Decorators** - experimental decorator syntax
- **Advanced types** - union types, intersection types, mapped types
- **Namespaces** - TypeScript namespace declarations
- **Module declarations** - ambient module declarations

### ❌ What This Plugin Does NOT Detect
- **Framework patterns** (use framework-specific plugins)
- **Runtime APIs** (use runtime-specific plugins like nodejs-plugin)
- **Build configuration** (tsconfig.json parsing - separate concern)

## Test Structure

### Test Categories

#### 1. Core TypeScript Features
**Focus**: Essential TypeScript language constructs

- **Type Annotations**: Function parameters, return types, variable types
- **Interfaces**: Interface declarations, extends, implements
- **Type Aliases**: Union types, intersection types, literal types
- **Enums**: Numeric enums, string enums, const enums

#### 2. Advanced Type System
**Focus**: Complex TypeScript type features

- **Generics**: Type parameters, constraints, default types
- **Mapped Types**: Utility types, conditional types
- **Advanced Types**: Partial, Pick, Omit, Record
- **Type Guards**: User-defined type guards

#### 3. Decorators and Metadata
**Focus**: Experimental decorator features

- **Class Decorators**: @Component, @Injectable, etc.
- **Method Decorators**: @Get, @Post, etc.
- **Property Decorators**: @Input, @Output, etc.
- **Parameter Decorators**: @Param, @Body, etc.

#### 4. Module System
**Focus**: TypeScript module features

- **ES6 Modules**: import/export with types
- **Namespaces**: TypeScript namespace syntax
- **Ambient Declarations**: declare module, declare global

## Test Assets

### Self-Contained Fixtures
**Location**: `src/plugins/typescript-plugin/tests/fixtures/`

#### `basic.ts` - Core TypeScript Features
```typescript
// Type annotations
function add(a: number, b: number): number {
    return a + b;
}

// Interfaces
interface User {
    id: number;
    name: string;
    email: string;
}

// Type aliases
type Status = 'active' | 'inactive' | 'pending';

// Enums
enum Role {
    ADMIN = 'admin',
    USER = 'user'
}

// Generics
function identity<T>(arg: T): T {
    return arg;
}

// Classes with TypeScript features
class UserService implements Repository<User> {
    private users: User[] = [];
    
    async findById(id: number): Promise<User | null> {
        return this.users.find(user => user.id === id) || null;
    }
}

// Decorators
@Injectable()
class DecoratedService {
    @deprecated('Use newMethod instead')
    oldMethod(): void {}
}
```

### Expected Detections
For `basic.ts`, the plugin should detect:

#### Types and Interfaces
- **Interfaces**: `User`, `Calculator`, `DataProcessor`, `Repository`
- **Type Aliases**: `Status`, `EventHandler`, `MathOperation`
- **Enums**: `Role`, `Color`

#### Generic Constructs
- **Generic Functions**: `identity<T>`, `mapArray<T, U>`
- **Generic Classes**: `GenericRepository<T>`
- **Generic Interfaces**: `Repository<T, K>`

#### Decorators
- **Class Decorators**: `@Injectable`, `@deprecated`
- **Method Decorators**: `@deprecated`, `@validate`
- **Parameter Decorators**: Various parameter decorations

#### Advanced Features
- **Conditional Types**: `NonNullable<T>`
- **Mapped Types**: `ReadonlyUser`, `Partial<T>`
- **Utility Types**: Usage of built-in utility types

## Test Execution

### Running Tests
```bash
# Run only TypeScript plugin tests
npm test src/plugins/typescript-plugin/tests/

# Run with coverage
npm test -- --coverage src/plugins/typescript-plugin/

# Run specific test
npm test -- --grep "should detect interfaces"
```

### Test Dependencies
- **Inherits from JavaScript Plugin**: All JavaScript detection capabilities
- **Tree-sitter TypeScript**: For syntax parsing
- **Future: TypeScript Compiler API**: For semantic analysis

## Integration with Other Plugins

### JavaScript Plugin Inheritance
- TypeScript plugin extends JavaScript plugin capabilities
- Inherits all JavaScript syntax detection
- Adds TypeScript-specific analysis on top

### Framework Plugin Integration
- **NestJS Plugin**: Uses TypeScript decorators detected by this plugin
- **Angular Plugin**: Would use TypeScript interfaces and decorators
- **React Plugin**: Could use TypeScript prop types and interfaces

### Shared Data
The plugin provides shared data for other plugins:
- `typescript-ast`: Parsed TypeScript AST
- `typescript-types`: Detected type information
- `typescript-interfaces`: Interface definitions
- `typescript-decorators`: Decorator metadata

## Future Enhancements

### High Priority
- [ ] **TypeScript Compiler API Integration** - Add semantic type analysis
- [ ] **Better Generic Detection** - Improve complex generic pattern recognition
- [ ] **Type Relationship Mapping** - Track type dependencies and relationships

### Medium Priority
- [ ] **JSDoc Type Analysis** - Parse JSDoc type annotations
- [ ] **Declaration File Support** - Analyze .d.ts files
- [ ] **Module Resolution** - Track import/export relationships

### Low Priority
- [ ] **Performance Optimization** - Cache parsed ASTs
- [ ] **Error Recovery** - Better handling of malformed TypeScript
- [ ] **Custom Transformer Support** - Plugin system for custom analysis

## TypeScript Compiler API Integration (Planned)

### Benefits
- **Semantic Analysis**: Understanding of type relationships
- **Type Checking**: Validation of type correctness
- **Symbol Resolution**: Proper import/export tracking
- **IntelliSense Data**: Rich metadata for IDE features

### Implementation Strategy
```typescript
import * as ts from 'typescript';

class TypeScriptPlugin extends JavaScriptPlugin {
    private program?: ts.Program;
    
    async analyze(context: PluginAnalysisContext) {
        // Tree-sitter for syntax (fast)
        const syntaxResult = await super.analyze(context);
        
        // TypeScript Compiler API for semantics (rich)
        const semanticResult = await this.analyzeSemantics(context);
        
        // Merge results
        return this.mergeResults(syntaxResult, semanticResult);
    }
}
```

## Maintenance Guidelines

### When to Update Tests
- New TypeScript language features (5.0+)
- Changes to decorator syntax (when stabilized)
- Bug fixes in type detection
- Framework integration requirements

### Test Asset Guidelines
1. **Focus on TypeScript-specific features** - Don't duplicate JavaScript tests
2. **Use realistic type patterns** - Based on actual TypeScript codebases
3. **Test edge cases** - Complex generics, conditional types, etc.
4. **Document expectations** - Clear comments about what should be detected
5. **Keep current** - Update for new TypeScript versions

### Performance Considerations
- Keep test files reasonably sized
- Use focused tests for specific features
- Consider async/await patterns for large files
- Mock external dependencies appropriately

## Recommended Test Repositories

### **Small Projects** (< 10k lines)
Perfect for validating TypeScript-specific features:

#### **type-fest** (~3k lines)
```bash
git clone https://github.com/sindresorhus/type-fest.git test-repositories/type-fest
```
**Why**: Collection of essential TypeScript types
- **Types**: Hundreds of utility types and type transformations
- **Interfaces**: Complex interface definitions
- **Generics**: Advanced generic patterns
- **Expected Elements**: ~200 types, ~50 interfaces, minimal functions

#### **zod** (~8k lines)
```bash
git clone https://github.com/colinhacks/zod.git test-repositories/zod
```
**Why**: TypeScript-first schema validation library
- **Classes**: Schema classes with complex inheritance
- **Types**: Runtime type validation patterns
- **Generics**: Heavy use of TypeScript generics
- **Expected Elements**: ~50 classes, ~100 types, ~200 functions

### **Medium Projects** (10k-100k lines)
For testing TypeScript analysis at scale:

#### **TypeORM** (~50k lines)
```bash
git clone https://github.com/typeorm/typeorm.git test-repositories/typeorm
```
**Why**: Complex TypeScript ORM with decorators
- **Decorators**: Extensive use of experimental decorators
- **Classes**: Entity classes, repository patterns
- **Interfaces**: Database schema interfaces
- **Expected Elements**: ~300 classes, ~200 interfaces, ~500 decorators

#### **Prisma Client** (~30k lines)
```bash
git clone https://github.com/prisma/prisma.git test-repositories/prisma
```
**Why**: Modern database toolkit with generated TypeScript
- **Generated Types**: Auto-generated TypeScript interfaces
- **Classes**: Client classes, query builders
- **Generics**: Complex generic patterns for type safety
- **Expected Elements**: ~200 interfaces, ~100 classes, ~300 types

### **Large Projects** (100k+ lines)
For stress testing TypeScript analysis:

#### **VS Code** (~500k lines)
```bash
git clone https://github.com/microsoft/vscode.git test-repositories/vscode
```
**Why**: Large-scale TypeScript application
- **Interfaces**: Thousands of interface definitions
- **Classes**: Complex class hierarchies
- **Modules**: Extensive module system
- **Expected Elements**: ~2000 interfaces, ~1000 classes, ~5000 functions

#### **Angular** (~300k lines)
```bash
git clone https://github.com/angular/angular.git test-repositories/angular
```
**Why**: Framework with heavy decorator usage
- **Decorators**: @Component, @Injectable, @Input, etc.
- **Classes**: Component classes, service classes
- **Interfaces**: Lifecycle interfaces, configuration interfaces
- **Expected Elements**: ~1000 decorators, ~800 classes, ~500 interfaces
