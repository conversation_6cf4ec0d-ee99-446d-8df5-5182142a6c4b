# 🧪 Test Repositories

This directory contains well-known repositories for testing the plugin system on real-world codebases. Each plugin has specific repository recommendations organized by project size and complexity.

## 🎯 **Plugin-Specific Testing Strategy**

Our plugin architecture requires targeted testing:
- **JavaScript Plugin**: Pure JavaScript language analysis
- **TypeScript Plugin**: TypeScript type system + inherits JavaScript
- **NestJS Plugin**: Framework patterns + uses TypeScript plugin

Each plugin's test README contains detailed repository recommendations.

## 📋 **Recommended Repositories to Clone**

### **🟢 JavaScript Projects**

1. **Express.js** - Popular Node.js framework
   ```bash
   git clone https://github.com/expressjs/express.git
   cd express
   npm install
   ```
   - **Why**: Classic JavaScript patterns, middleware, routing
   - **Size**: Medium (~50k lines)
   - **Patterns**: CommonJS modules, callbacks, middleware patterns

2. **Lodash** - Utility library
   ```bash
   git clone https://github.com/lodash/lodash.git
   cd lodash
   npm install
   ```
   - **Why**: Pure JavaScript, functional programming patterns
   - **Size**: Large (~100k lines)
   - **Patterns**: Functional programming, utility functions

### **🔷 TypeScript Projects**

3. **TypeScript Compiler** - Self-hosting TypeScript project
   ```bash
   git clone https://github.com/microsoft/TypeScript.git
   cd TypeScript
   npm install
   ```
   - **Why**: Complex TypeScript patterns, compiler implementation
   - **Size**: Very Large (~500k lines)
   - **Patterns**: Advanced TypeScript, generics, complex types

4. **VS Code** - Popular editor
   ```bash
   git clone https://github.com/microsoft/vscode.git
   cd vscode
   npm install
   ```
   - **Why**: Large TypeScript codebase, Electron app
   - **Size**: Very Large (~1M lines)
   - **Patterns**: Complex architecture, extensions, UI patterns

### **🚀 NestJS Projects**

5. **NestJS Framework** - The framework itself
   ```bash
   git clone https://github.com/nestjs/nest.git
   cd nest
   npm install
   ```
   - **Why**: Pure NestJS patterns, decorators, DI
   - **Size**: Large (~200k lines)
   - **Patterns**: Decorators, dependency injection, modules

6. **NestJS Realworld Example** - Full application
   ```bash
   git clone https://github.com/lujakob/nestjs-realworld-example-app.git
   cd nestjs-realworld-example-app
   npm install
   ```
   - **Why**: Real-world NestJS application patterns
   - **Size**: Small (~5k lines)
   - **Patterns**: Controllers, services, modules, authentication

7. **NestJS Microservices** - Microservices example
   ```bash
   git clone https://github.com/nestjs/nest-microservices-example.git
   cd nest-microservices-example
   npm install
   ```
   - **Why**: Microservices patterns, message queues
   - **Size**: Small (~3k lines)
   - **Patterns**: Microservices, event-driven architecture

### **⚛️ React Projects**

8. **React** - The framework itself
   ```bash
   git clone https://github.com/facebook/react.git
   cd react
   npm install
   ```
   - **Why**: React internals, complex patterns
   - **Size**: Very Large (~500k lines)
   - **Patterns**: Hooks, components, JSX, advanced React

9. **Create React App** - Popular starter
   ```bash
   git clone https://github.com/facebook/create-react-app.git
   cd create-react-app
   npm install
   ```
   - **Why**: Standard React application structure
   - **Size**: Medium (~50k lines)
   - **Patterns**: Standard React patterns, build tools

10. **Next.js** - React framework
    ```bash
    git clone https://github.com/vercel/next.js.git
    cd next.js
    npm install
    ```
    - **Why**: Server-side rendering, API routes
    - **Size**: Very Large (~800k lines)
    - **Patterns**: SSR, API routes, React patterns

### **🔄 Full-Stack Projects**

11. **Supabase** - Backend-as-a-Service
    ```bash
    git clone https://github.com/supabase/supabase.git
    cd supabase
    ```
    - **Why**: Mix of TypeScript, React, and backend services
    - **Size**: Very Large (~1M lines)
    - **Patterns**: Full-stack, microservices, React dashboard

12. **Grafana** - Monitoring dashboard
    ```bash
    git clone https://github.com/grafana/grafana.git
    cd grafana
    npm install
    ```
    - **Why**: Complex TypeScript/React application
    - **Size**: Very Large (~1M lines)
    - **Patterns**: Data visualization, plugins, complex UI

## 🎯 **Testing Strategy**

### **Phase 1: Small Projects (Week 1)**
- Start with **NestJS Realworld Example** (~5k lines)
- Test basic plugin functionality
- Verify decorator detection and DI analysis

### **Phase 2: Medium Projects (Week 2)**
- Test on **Express.js** (~50k lines)
- Test on **Create React App** (~50k lines)
- Verify performance and memory usage

### **Phase 3: Large Projects (Week 3)**
- Test on **NestJS Framework** (~200k lines)
- Test on **TypeScript Compiler** (~500k lines)
- Optimize for large codebases

### **Phase 4: Very Large Projects (Week 4)**
- Test on **VS Code** (~1M lines)
- Test on **Next.js** (~800k lines)
- Performance optimization and incremental analysis

## 📊 **Test Metrics to Track**

### **Accuracy Metrics**
- **Decorator Detection Rate**: % of decorators correctly identified
- **Function Detection Rate**: % of functions correctly identified
- **Class Detection Rate**: % of classes correctly identified
- **Import/Export Detection Rate**: % of dependencies correctly identified

### **Performance Metrics**
- **Analysis Time**: Time to analyze per file/project
- **Memory Usage**: Peak memory usage during analysis
- **Throughput**: Files analyzed per second
- **Scalability**: Performance vs codebase size

### **Quality Metrics**
- **False Positives**: Incorrectly identified elements
- **False Negatives**: Missed elements
- **Context Relevance**: Quality of extracted context
- **Token Efficiency**: Context size vs information density

## 🔧 **Test Commands**

```bash
# Test on small NestJS project
cyzer analyze test-repositories/nestjs-realworld-example-app --config nestjs-backend

# Test context extraction
cyzer context test-repositories/nestjs-realworld-example-app/src/user/user.controller.ts --max-tokens 1000

# Performance test on large project
cyzer analyze test-repositories/TypeScript --config typescript-project --performance-mode

# Compare different configurations
cyzer analyze test-repositories/react --config react-frontend
cyzer analyze test-repositories/react --config typescript-project
```

## 📈 **Expected Results**

### **Small Projects (< 10k lines)**
- **Analysis Time**: < 5 seconds
- **Memory Usage**: < 100MB
- **Accuracy**: > 95%

### **Medium Projects (10k-100k lines)**
- **Analysis Time**: < 30 seconds
- **Memory Usage**: < 500MB
- **Accuracy**: > 90%

### **Large Projects (100k-500k lines)**
- **Analysis Time**: < 2 minutes
- **Memory Usage**: < 2GB
- **Accuracy**: > 85%

### **Very Large Projects (> 500k lines)**
- **Analysis Time**: < 10 minutes
- **Memory Usage**: < 4GB
- **Accuracy**: > 80%

## 🚀 **Clone All Repositories Script**

```bash
#!/bin/bash
# clone-test-repos.sh

mkdir -p test-repositories
cd test-repositories

echo "Cloning JavaScript projects..."
git clone https://github.com/expressjs/express.git
git clone https://github.com/lodash/lodash.git

echo "Cloning TypeScript projects..."
git clone https://github.com/microsoft/TypeScript.git
git clone https://github.com/microsoft/vscode.git

echo "Cloning NestJS projects..."
git clone https://github.com/nestjs/nest.git
git clone https://github.com/lujakob/nestjs-realworld-example-app.git

echo "Cloning React projects..."
git clone https://github.com/facebook/react.git
git clone https://github.com/facebook/create-react-app.git
git clone https://github.com/vercel/next.js.git

echo "Cloning Full-stack projects..."
git clone https://github.com/supabase/supabase.git

echo "Done! Test repositories cloned."
```

Save this as `clone-test-repos.sh` and run:
```bash
chmod +x clone-test-repos.sh
./clone-test-repos.sh
```
