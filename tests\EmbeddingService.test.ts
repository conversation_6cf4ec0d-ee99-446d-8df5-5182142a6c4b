import { EmbeddingService } from '../src/services/EmbeddingService.js';
import { CodeElement, CodeElementType } from '../src/types/CodeElement.js';
import { vi } from 'vitest';

describe('EmbeddingService', () => {
    let embeddingService: EmbeddingService;
    let originalApiKey: string | undefined;

    beforeEach(() => {
        // Store original API key
        originalApiKey = process.env.GEMINI_API_KEY;
    });

    afterEach(() => {
        // Restore original API key
        if (originalApiKey) {
            process.env.GEMINI_API_KEY = originalApiKey;
        } else {
            delete process.env.GEMINI_API_KEY;
        }
    });

    describe('Initialization', () => {
        it('should initialize without throwing when API key is missing', () => {
            // Temporarily remove API key
            delete process.env.GEMINI_API_KEY;
            expect(() => new EmbeddingService()).not.toThrow();
        });

        it('should warn when API key is missing', () => {
            // Temporarily remove API key
            delete process.env.GEMINI_API_KEY;
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            new EmbeddingService();
            expect(consoleSpy).toHaveBeenCalledWith(
                'GEMINI_API_KEY not found in .env file. EmbeddingService will not be able to generate embeddings.'
            );
            consoleSpy.mockRestore();
        });
    });

    describe('generateEmbeddings', () => {
        it('should return empty array when API key is not configured', async () => {
            // Temporarily remove API key
            delete process.env.GEMINI_API_KEY;
            embeddingService = new EmbeddingService();

            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false,
                    fullText: 'function testFunction() { return 42; }'
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(result).toEqual([]);
        });

        it('should skip elements without fullText', async () => {
            // Temporarily remove API key to test skipping behavior
            delete process.env.GEMINI_API_KEY;
            embeddingService = new EmbeddingService();

            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false
                    // No fullText property
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(result).toEqual([]);
        });

        it('should skip elements with empty fullText', async () => {
            // Temporarily remove API key to test skipping behavior
            delete process.env.GEMINI_API_KEY;
            embeddingService = new EmbeddingService();

            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false,
                    fullText: '   ' // Only whitespace
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(result).toEqual([]);
        });

        // Test for when API key is available (using real API key from .env)
        it('should process elements with valid fullText when API key is available', async () => {
            // Use the real API key from .env for this test
            embeddingService = new EmbeddingService();

            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false,
                    fullText: 'function testFunction() { return 42; }'
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(Array.isArray(result)).toBe(true);

            // If API key is configured, we should get embeddings
            if (process.env.GEMINI_API_KEY) {
                expect(result.length).toBeGreaterThan(0);
                expect(result[0]).toHaveProperty('embedding');
                expect(result[0]).toHaveProperty('embeddingModel');
                expect(Array.isArray(result[0].embedding)).toBe(true);
            }
        });
    });

    describe('embedQuery', () => {
        it('should return null when API key is not configured', async () => {
            // Temporarily remove API key
            delete process.env.GEMINI_API_KEY;
            embeddingService = new EmbeddingService();

            const result = await embeddingService.embedQuery('test query');
            expect(result).toBeNull();
        });

        it('should return null for empty query text', async () => {
            embeddingService = new EmbeddingService();
            const result = await embeddingService.embedQuery('');
            expect(result).toBeNull();
        });

        it('should return null for whitespace-only query text', async () => {
            embeddingService = new EmbeddingService();
            const result = await embeddingService.embedQuery('   ');
            expect(result).toBeNull();
        });

        it('should log error when API key is missing', async () => {
            // Temporarily remove API key
            delete process.env.GEMINI_API_KEY;
            embeddingService = new EmbeddingService();

            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            await embeddingService.embedQuery('test query');
            expect(consoleSpy).toHaveBeenCalledWith(
                'Cannot embed query: GEMINI_API_KEY is not configured.'
            );
            consoleSpy.mockRestore();
        });

        it('should log warning for empty query', async () => {
            embeddingService = new EmbeddingService();
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            await embeddingService.embedQuery('');
            expect(consoleSpy).toHaveBeenCalledWith(
                'Cannot embed empty query text.'
            );
            consoleSpy.mockRestore();
        });
    });

    describe('Model Configuration', () => {
        it('should use the latest embedding model', () => {
            // This is more of a documentation test to ensure we're using the right model
            // The actual model name is checked in the implementation
            expect(true).toBe(true); // Placeholder - model is configured in the service
        });
    });
});
