/**
 * Plugin Integration Tests
 * 
 * Tests the complete plugin system working together.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createPluginRegistry } from '../../src/plugins/PluginRegistry.js';
import { PluginAnalysisContext } from '../../src/core/PluginSystem.js';
import { CodeElementType } from '../../src/types/CodeElement.js';
import * as fs from 'fs/promises';
import * as path from 'path';

describe('Plugin Integration', () => {
  let registry: any;

  beforeEach(async () => {
    registry = await createPluginRegistry('./src/plugins');
  });

  afterEach(async () => {
    if (registry) {
      await registry.cleanup();
    }
  });

  it('should auto-detect and analyze JavaScript project', async () => {
    // Load JavaScript configuration
    await registry.loadConfiguration('javascript-project', './src/plugins');

    // Load test file from plugin fixtures
    const testFilePath = path.join(process.cwd(), 'src/plugins/javascript-plugin/tests/fixtures/basic.js');
    const fileContent = await fs.readFile(testFilePath, 'utf-8');
    
    // Get plugins for JavaScript file
    const plugins = registry.getPluginsForFile('basic.js', 'javascript');
    expect(plugins.length).toBeGreaterThan(0);
    
    // Analyze with JavaScript plugin
    const jsPlugin = plugins[0];
    const context: PluginAnalysisContext = {
      filePath: 'test-files/javascript/basic.js',
      fileContent,
      language: 'javascript',
      projectRoot: './test-files',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };
    
    const result = await jsPlugin.analyze(context);
    
    expect(result.errors).toHaveLength(0);
    expect(result.elements.length).toBeGreaterThan(0);
    expect(result.metadata.language).toBe('javascript');
    
    // Should find functions, classes, variables
    const functions = result.elements.filter(e => e.type === CodeElementType.Function);
    const classes = result.elements.filter(e => e.type === CodeElementType.Class);
    const variables = result.elements.filter(e => e.type === CodeElementType.Variable);
    
    expect(functions.length).toBeGreaterThan(0);
    expect(classes.length).toBeGreaterThan(0);
    expect(variables.length).toBeGreaterThan(0);
  });

  it('should analyze TypeScript with decorators', async () => {
    // Load TypeScript configuration
    await registry.loadConfiguration('typescript-project', './src/plugins');

    // Load test file from plugin fixtures
    const testFilePath = path.join(process.cwd(), 'src/plugins/typescript-plugin/tests/fixtures/basic.ts');
    const fileContent = await fs.readFile(testFilePath, 'utf-8');
    
    // Get plugins for TypeScript file
    const plugins = registry.getPluginsForFile('basic.ts', 'typescript');
    expect(plugins.length).toBeGreaterThan(0);
    
    // Analyze with TypeScript plugin
    const tsPlugin = plugins[0];
    const context: PluginAnalysisContext = {
      filePath: 'src/plugins/typescript-plugin/tests/fixtures/basic.ts',
      fileContent,
      language: 'typescript',
      projectRoot: './src/plugins',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };
    
    const result = await tsPlugin.analyze(context);
    
    expect(result.errors).toHaveLength(0);
    expect(result.elements.length).toBeGreaterThan(0);
    expect(result.metadata.language).toBe('typescript');
    expect(result.metadata.hasDecorators).toBe(true);
    
    // Should find TypeScript-specific elements
    const interfaces = result.elements.filter(e => e.type === CodeElementType.Interface);
    const types = result.elements.filter(e => e.type === CodeElementType.Type);
    const decorators = result.elements.filter(e => e.metadata?.isDecorator === true);
    
    expect(interfaces.length).toBeGreaterThan(0);
    expect(types.length).toBeGreaterThan(0);
    expect(decorators.length).toBeGreaterThan(0);
  });

  it('should analyze NestJS project with framework patterns', async () => {
    // Load NestJS configuration
    await registry.loadConfiguration('nestjs-backend', './src/plugins');

    // Load test file from plugin fixtures
    const testFilePath = path.join(process.cwd(), 'src/plugins/nestjs-plugin/tests/fixtures/controller.ts');
    const fileContent = await fs.readFile(testFilePath, 'utf-8');
    
    // Get plugins for NestJS file
    const plugins = registry.getPluginsForFile('users.controller.ts', 'typescript', 'nestjs');
    expect(plugins.length).toBeGreaterThan(0);
    
    // Should include both TypeScript and NestJS plugins
    const pluginNames = plugins.map(p => p.metadata.name);
    expect(pluginNames).toContain('typescript-plugin');
    expect(pluginNames).toContain('nestjs-plugin');
    
    // Analyze with NestJS plugin (highest priority)
    const nestjsPlugin = plugins.find(p => p.metadata.name === 'nestjs-plugin');
    expect(nestjsPlugin).toBeDefined();
    
    const context: PluginAnalysisContext = {
      filePath: 'src/plugins/nestjs-plugin/tests/fixtures/controller.ts',
      fileContent,
      language: 'typescript',
      framework: 'nestjs',
      projectRoot: './src/plugins',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };
    
    const result = await nestjsPlugin!.analyze(context);
    
    expect(result.errors).toHaveLength(0);
    expect(result.elements.length).toBeGreaterThan(0);
    expect(result.metadata.framework).toBe('nestjs');
    
    // Should find NestJS-specific elements
    const controllers = result.elements.filter(e => e.type === CodeElementType.NestJSController);
    const endpoints = result.elements.filter(e => e.type === CodeElementType.NestJSEndpoint);
    
    expect(controllers.length).toBeGreaterThan(0);
    expect(endpoints.length).toBeGreaterThan(0);
    expect(result.metadata.hasGuards).toBe(true);
    expect(result.metadata.hasInterceptors).toBe(true);
  });

  it('should share data between plugins', async () => {
    // Load NestJS configuration (includes TypeScript plugin)
    await registry.loadConfiguration('nestjs-backend', './src/plugins');

    // Load test file from plugin fixtures
    const testFilePath = path.join(process.cwd(), 'src/plugins/nestjs-plugin/tests/fixtures/controller.ts');
    const fileContent = await fs.readFile(testFilePath, 'utf-8');
    
    // Get NestJS plugin
    const plugins = registry.getPluginsForFile('user.service.ts', 'typescript', 'nestjs');
    const nestjsPlugin = plugins.find(p => p.metadata.name === 'nestjs-plugin');
    
    const context: PluginAnalysisContext = {
      filePath: 'src/plugins/nestjs-plugin/tests/fixtures/controller.ts',
      fileContent,
      language: 'typescript',
      framework: 'nestjs',
      projectRoot: './src/plugins',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };
    
    const result = await nestjsPlugin!.analyze(context);
    
    // Should have shared data from TypeScript plugin
    expect(result.sharedData).toBeDefined();
    expect(result.sharedData?.has('typescript-elements')).toBe(true);
    expect(result.sharedData?.has('nestjs-elements')).toBe(true);
    expect(result.sharedData?.has('nestjs-architecture')).toBe(true);
  });

  it('should handle plugin dependencies correctly', async () => {
    // Load NestJS configuration
    await registry.loadConfiguration('nestjs-backend', './src/plugins');
    
    const config = registry.getCurrentConfig();
    expect(config?.plugins).toContain('typescript-plugin');
    expect(config?.plugins).toContain('nestjs-plugin');
    
    // TypeScript plugin should be loaded (dependency of NestJS)
    const allPlugins = registry.getAllPlugins();
    const pluginNames = allPlugins.map(p => p.metadata.name);
    
    expect(pluginNames).toContain('typescript-plugin');
    expect(pluginNames).toContain('nestjs-plugin');
  });

  it('should prioritize plugins correctly', async () => {
    // Load NestJS configuration
    await registry.loadConfiguration('nestjs-backend', './src/plugins');
    
    // Get plugins for NestJS file
    const plugins = registry.getPluginsForFile('users.controller.ts', 'typescript', 'nestjs');
    
    // Should be sorted by priority (higher first)
    for (let i = 0; i < plugins.length - 1; i++) {
      expect(plugins[i].metadata.priority).toBeGreaterThanOrEqual(plugins[i + 1].metadata.priority);
    }
    
    // NestJS plugin should have higher priority than TypeScript plugin
    const nestjsPlugin = plugins.find(p => p.metadata.name === 'nestjs-plugin');
    const tsPlugin = plugins.find(p => p.metadata.name === 'typescript-plugin');
    
    if (nestjsPlugin && tsPlugin) {
      expect(nestjsPlugin.metadata.priority).toBeGreaterThan(tsPlugin.metadata.priority);
    }
  });

  it('should handle multiple file types in same project', async () => {
    // Load NestJS configuration
    await registry.loadConfiguration('nestjs-backend', './src/plugins');
    
    const testFiles = [
      { path: 'users.controller.ts', language: 'typescript', framework: 'nestjs' },
      { path: 'user.service.ts', language: 'typescript', framework: 'nestjs' },
      { path: 'users.module.ts', language: 'typescript', framework: 'nestjs' }
    ];
    
    for (const testFile of testFiles) {
      const plugins = registry.getPluginsForFile(testFile.path, testFile.language, testFile.framework);
      expect(plugins.length).toBeGreaterThan(0);
      
      // Should include NestJS plugin for all NestJS files
      const hasNestJSPlugin = plugins.some(p => p.metadata.name === 'nestjs-plugin');
      expect(hasNestJSPlugin).toBe(true);
    }
  });

  it('should handle errors gracefully', async () => {
    // Load TypeScript configuration
    await registry.loadConfiguration('typescript-project', './src/plugins');
    
    // Try to analyze malformed TypeScript
    const plugins = registry.getPluginsForFile('malformed.ts', 'typescript');
    const tsPlugin = plugins[0];
    
    const context: PluginAnalysisContext = {
      filePath: 'malformed.ts',
      fileContent: 'interface Incomplete {',
      language: 'typescript',
      projectRoot: './src/plugins',
      options: { includeElementContent: true, indexForSearch: false },
      sharedData: new Map()
    };
    
    const result = await tsPlugin.analyze(context);
    
    // Should not crash, should return a result
    expect(result).toBeDefined();
    expect(result.confidence).toBeDefined();
    expect(result.analysisTime).toBeGreaterThan(0);
  });
});
